# Exercise 模块架构分析

## 概述

本文档分析了从 `apps/stu/app/exercise/page.tsx` 到 `packages/core/src/new-exercise/view/main-view.tsx` 的完整架构，包括组件层次、数据流、依赖关系和设计模式。

## 架构层次

### 1. 应用入口层 (apps/stu/app/exercise/)

#### ExercisePreviewPage
- **职责**: 主页面入口，处理状态栏设置和 Suspense 包装
- **关键功能**: 
  - 设置状态栏可见性
  - 提供 Loading 状态
  - 包装 ClientProvider

#### ExercisePageContent  
- **职责**: 处理客户端水合和路由逻辑
- **状态管理**:
  - `isClientReady`: 客户端准备状态
  - `finalStudyType`: 最终学习类型
- **路由逻辑**: 根据 studyType 路由到不同的 Entry 组件

### 2. 应用适配层 (apps/stu/app/views/exercise/)

#### BaseExerciseEntry
- **职责**: 基础练习入口，处理设备集成和业务回调
- **关键功能**:
  - 获取 URL 参数 (studySessionId, studyType)
  - 初始化客户端上下文
  - 处理设备返回按钮
  - 集成"问一问"功能
  - 提供头部右侧渲染函数

#### WrongBookExerciseEntry
- **职责**: 错题本练习入口
- **特殊处理**: 错题本特有的业务逻辑

### 3. 核心视图层 (packages/core/src/new-exercise/view/)

#### ExerciseView (main-view.tsx)
- **职责**: 核心练习视图容器
- **架构特点**:
  - 使用 useQuestionStore 创建状态管理器
  - 包装多层 Context Provider
  - 处理初始化参数验证

#### ExerciseViewContent
- **职责**: 实际的练习内容渲染
- **UI 结构**:
  - 头部区域 (返回按钮、计时器、进度条)
  - 主体区域 (题目视图)
  - 转场动画组件

### 4. 状态管理层 (packages/core/src/new-exercise/store/)

#### useQuestionStore
- **技术栈**: @preact-signals/safe-react
- **核心状态**:
  - `rootQuestion`: 根级题目信息
  - `userAnswerDataMap`: 用户答案映射
  - `questionStatus`: 题目状态
  - `timerState`: 计时器状态
  - `progressBarState`: 进度条状态
  - `transitionState`: 转场动画状态

- **核心方法**:
  - `updateUserAnswer()`: 更新用户答案
  - `updateQuestionStatus()`: 更新题目状态
  - `handleNextQuestion()`: 处理下一题
  - `resetQuestionStateOnNext()`: 重置题目状态

### 5. 上下文层 (packages/core/src/new-exercise/context/)

#### ExerciseContextProvider
- **职责**: 统一的练习上下文提供者
- **集成功能**:
  - 集成多个 ViewModel
  - 提供类型安全的状态访问
  - 统一依赖注入

#### 其他 Context
- `StudyTypeThemeProvider`: 主题上下文
- `QuestionViewProvider`: 题目视图上下文
- `WrongBookProvider`: 错题本上下文

### 6. 业务逻辑层 (packages/core/src/new-exercise/viewmodels/)

#### useQuestionViewModel
- **职责**: 核心题目业务逻辑
- **关键方法**:
  - `handleContinue()`: 处理继续逻辑
  - `handleContinueWithTransitions()`: 带转场的继续
  - `handleResumeAnimation()`: 恢复动画处理

#### 其他 ViewModel
- `useTimerVM`: 计时器逻辑
- `useExitViewModel`: 退出逻辑
- `useAnimationTransitionVM`: 转场动画逻辑

### 7. 数据模型层 (packages/core/src/new-exercise/models/)

#### exercise-model.ts
- **API Hooks**:
  - `useSubmitStudyAnswer()`: 提交答案
  - `useGetNextQuestion()`: 获取下一题
  - `useExitStudySession()`: 退出会话
  - `useCheckAnswerPicture()`: 校验图片答案

### 8. 策略模式层 (packages/core/src/new-exercise/strategies/)

#### BaseQuestionStrategy
- **设计模式**: 策略模式
- **职责**: 不同题型的验证逻辑
- **具体策略**:
  - ChoiceStrategy: 选择题策略
  - FillBlankStrategy: 填空题策略
  - ParentChildStrategy: 母子题策略

## 设计模式

### 1. MVVM 架构模式
- **Model**: exercise-model.ts (API 调用)
- **ViewModel**: viewmodels/ (业务逻辑)
- **View**: view/ (UI 组件)

### 2. 分层架构模式
- **表现层**: Page, Entry 组件
- **应用层**: View 组件, Context
- **业务层**: ViewModel, Strategy
- **数据层**: Store, Model

### 3. 策略模式
- 抽象策略: BaseQuestionStrategy
- 具体策略: 各种题型策略
- 策略工厂: StrategyFactory

### 4. Context Provider 模式
- 多层 Context 嵌套
- 依赖注入和状态共享

## 技术特点

### 1. 现代化状态管理
- 使用 @preact-signals/safe-react
- 响应式状态更新
- 性能优化的 Signal 机制

### 2. 类型安全
- 严格的 TypeScript 类型定义
- 接口驱动的设计
- 编译时类型检查

### 3. 性能优化
- React.memo 防止不必要重渲染
- useCallback 缓存函数引用
- Suspense 懒加载
- 批量状态更新

## 数据流

```
URL 参数 → Entry 组件 → ExerciseView → Context Provider → Store/ViewModel → API Model → 后端服务
```

### 初始化流程
1. 页面加载 → 解析 URL 参数
2. 创建 clientContext → 初始化 questionStore
3. 包装 Context Provider → 渲染核心视图
4. 获取初始题目 → 更新状态 → 渲染 UI

### 用户交互流程
1. 用户操作 → View 组件事件
2. 调用 ViewModel 方法 → 更新 Store 状态
3. Signal 状态变化 → 触发组件重渲染
4. API 调用 → 更新服务端状态

## 依赖关系

### 垂直依赖 (分层)
- 上层依赖下层，下层不依赖上层
- 清晰的职责分离
- 便于测试和维护

### 水平依赖 (协作)
- ViewModel 之间相互协作
- Strategy 独立实现
- Components 组合使用

## 架构类图

### 整体架构类图

```mermaid
classDiagram
    %% 应用入口层
    class ExercisePreviewPage {
        +render() ReactElement
        +setStatusBar() void
    }

    class ExercisePageContent {
        -isClientReady: boolean
        -finalStudyType: StudyType
        +checkUrlParams() void
        +render() ReactElement
    }

    %% 应用适配层
    class BaseExerciseEntry {
        -clientContext: ClientContext
        -baseUrlParams: URLParams
        -askData: AskData
        +handleBack() void
        +renderHeaderRight() ReactNode
    }

    class WrongBookExerciseEntry {
        +render() ReactElement
    }

    %% 核心视图层
    class ExerciseView {
        -questionStore: QuestionStoreType
        +initializeStore() QuestionStoreType
        +renderContent() ReactElement
    }

    class ExerciseViewContent {
        +render() ReactElement
        +handleDeviceBack() void
    }

    %% 状态管理层
    class QuestionStore {
        -rootQuestion: Signal~StudyQuestionInfo~
        -userAnswerDataMap: Signal~Map~
        -questionStatus: Signal~QuestionStatus~
        -timerState: Signal~TimerState~
        -progressBarState: Signal~ProgressBarProps~
        -transitionState: Signal~TransitionState~
        +updateUserAnswer() void
        +updateQuestionStatus() void
        +handleNextQuestion() void
        +resetQuestionStateOnNext() void
        +clearUserAnswer() void
        +updateSelfEvaluation() void
    }

    %% 上下文层
    class ExerciseContextProvider {
        -questionStore: QuestionStoreType
        -clientContext: ClientContext
        +provideContext() ContextValue
    }

    class StudyTypeThemeProvider {
        -studyType: StudyType
        -customVariables: Record
        +provideTheme() void
    }

    %% 业务逻辑层
    class QuestionViewModel {
        -hasPlayedResumeAnimationRef: RefObject
        +handleContinue() void
        +handleContinueWithTransitions() Promise~void~
        +handleResumeAnimation() Promise~void~
    }

    class TimerVM {
        -isTimerActive: boolean
        +handleTimeUpdate() void
        +startTimer() void
        +stopTimer() void
    }

    class ExitViewModel {
        +handleExitRequest() void
        +confirmExit() void
        +cancelExit() void
    }

    %% 数据模型层
    class ExerciseModel {
        +useSubmitStudyAnswer() SubmitHook
        +useGetNextQuestion() QuestionHook
        +useExitStudySession() ExitHook
        +useCheckAnswerPicture() CheckHook
    }

    %% 策略模式层
    class BaseQuestionStrategy {
        <<abstract>>
        +type: QuestionRenderType
        +name: string
        +validateBeforeSubmit()* BeforeSubmitValidationResult
    }

    class ChoiceStrategy {
        +validateBeforeSubmit() BeforeSubmitValidationResult
    }

    class FillBlankStrategy {
        +validateBeforeSubmit() BeforeSubmitValidationResult
    }

    class StrategyFactory {
        +createStrategy() BaseQuestionStrategy
        +registerStrategy() void
        +getSupportedTypes() QuestionRenderType[]
    }

    %% 依赖关系
    ExercisePreviewPage --> ExercisePageContent
    ExercisePageContent --> BaseExerciseEntry
    ExercisePageContent --> WrongBookExerciseEntry
    BaseExerciseEntry --> ExerciseView
    WrongBookExerciseEntry --> ExerciseView

    ExerciseView --> QuestionStore : creates
    ExerciseView --> StudyTypeThemeProvider : wraps
    ExerciseView --> ExerciseContextProvider : wraps
    ExerciseView --> ExerciseViewContent : renders

    ExerciseContextProvider --> QuestionStore : uses
    ExerciseContextProvider --> QuestionViewModel : integrates
    ExerciseContextProvider --> TimerVM : integrates
    ExerciseContextProvider --> ExitViewModel : integrates

    QuestionViewModel --> QuestionStore : uses
    QuestionViewModel --> ExerciseModel : calls
    TimerVM --> QuestionStore : updates
    ExitViewModel --> QuestionStore : reads

    QuestionStore --> BaseQuestionStrategy : uses
    StrategyFactory --> BaseQuestionStrategy : creates
    StrategyFactory --> ChoiceStrategy : creates
    StrategyFactory --> FillBlankStrategy : creates

    BaseQuestionStrategy <|-- ChoiceStrategy
    BaseQuestionStrategy <|-- FillBlankStrategy
```

### 组件层次结构图

```mermaid
graph TD
    A[ExercisePreviewPage] --> B[ExercisePageContent]
    B --> C[BaseExerciseEntry]
    B --> D[WrongBookExerciseEntry]

    C --> E[ExerciseView]
    D --> E

    E --> F[StudyTypeThemeProvider]
    F --> G[ExerciseContextProvider]
    G --> H[ExerciseViewContent]

    H --> I[Header Area]
    H --> J[QuestionViewContent]
    H --> K[TransitionView]

    I --> L[BackButton]
    I --> M[TimerDisplay]
    I --> N[ProgressBar]
    I --> O[ExerciseHeaderActionView]
    I --> P[FeedbackView]

    J --> Q[QuestionViewProvider]
    Q --> R[WrongBookProvider]
    R --> S[QuestionView]
    R --> T[ExitView]

    S --> U[QuestionContent]
    S --> V[AnsweringArea]
    S --> W[ExplanationView]
    S --> X[QuestionActionButtonsView]
    S --> Y[ExerciseConfirmDialog]

    style A fill:#e1f5fe
    style E fill:#f3e5f5
    style G fill:#e8f5e8
    style S fill:#fff3e0
```

### 数据流图

```mermaid
sequenceDiagram
    participant User
    participant Page as ExercisePreviewPage
    participant Entry as BaseExerciseEntry
    participant View as ExerciseView
    participant Context as ExerciseContextProvider
    participant Store as QuestionStore
    participant VM as QuestionViewModel
    participant Model as ExerciseModel
    participant API as Backend API

    User->>Page: 访问页面
    Page->>Entry: 路由到Entry组件
    Entry->>Entry: 解析URL参数
    Entry->>Entry: 初始化clientContext
    Entry->>View: 传递初始化参数

    View->>Store: 创建questionStore
    View->>Context: 包装Context Provider
    Context->>VM: 集成ViewModel

    VM->>Model: 调用useGetNextQuestion
    Model->>API: 发起API请求
    API-->>Model: 返回题目数据
    Model-->>VM: 返回题目信息
    VM->>Store: 更新题目状态
    Store-->>View: 触发重渲染

    User->>View: 用户答题
    View->>VM: 调用updateUserAnswer
    VM->>Store: 更新答案状态
    Store-->>View: 更新UI显示

    User->>View: 提交答案
    View->>VM: 调用handleContinue
    VM->>Model: 调用useSubmitStudyAnswer
    Model->>API: 提交答案
    API-->>Model: 返回判题结果
    Model-->>VM: 返回提交结果
    VM->>Store: 更新题目状态
    Store-->>View: 显示结果反馈
```

### 状态管理架构图

```mermaid
graph TB
    subgraph "Signal State Management"
        A[rootQuestion: Signal]
        B[userAnswerDataMap: Signal]
        C[questionStatus: Signal]
        D[timerState: Signal]
        E[progressBarState: Signal]
        F[transitionState: Signal]
        G[confirmDialogState: Signal]
    end

    subgraph "Computed Values"
        H[currentQuestion: Computed]
        I[isSubjectiveQuestion: Computed]
        J[canSubmitSelfEvaluation: Computed]
        K[hasNextQuestion: Computed]
    end

    subgraph "ViewModel Actions"
        L[updateUserAnswer]
        M[updateQuestionStatus]
        N[handleNextQuestion]
        O[handleContinue]
        P[resetQuestionState]
    end

    subgraph "React Components"
        Q[QuestionView]
        R[AnsweringArea]
        S[ProgressBar]
        T[TimerDisplay]
        U[ActionButtons]
    end

    A --> H
    A --> I
    B --> J
    F --> K

    L --> B
    M --> C
    N --> A
    O --> D
    P --> A
    P --> B
    P --> C

    H --> Q
    B --> R
    E --> S
    D --> T
    C --> U

    style A fill:#ffebee
    style B fill:#ffebee
    style C fill:#ffebee
    style H fill:#e3f2fd
    style I fill:#e3f2fd
    style L fill:#e8f5e8
    style M fill:#e8f5e8
```

