"use client";

import { Loading } from "@/app/components/common/loading";
import { StudyType } from "@repo/core/enums";
import { getStudyTypeFromUrl } from "@repo/core/new-exercise/utils";
import { Suspense, useEffect, useState } from "react";
import { ClientProvider } from "../providers/client-provider";
import { setStatusBar } from "../utils/device";
import BaseExerciseEntry from "../views/exercise/base-exercise-entry";
import WrongBookExerciseEntry from "../views/exercise/wrong-book-exercise-entry";

function ExercisePageContent() {
  // 🔑 使用 state 来控制组件渲染，避免服务端渲染与客户端水合不一致
  const [isClientReady, setIsClientReady] = useState(false);
  const [finalStudyType, setFinalStudyType] = useState<StudyType | null>(null);

  // 在客户端水合完成后重新检查 URL 参数
  useEffect(() => {
    // 🔑 在客户端水合完成后，重新检查 URL 参数
    const urlParams = new URLSearchParams(window.location.search);
    const urlStudyType = urlParams.get("studyType");

    if (urlStudyType === "8") {
      setFinalStudyType(StudyType.WRONG_QUESTION_BANK);
    } else {
      const parsedStudyType = getStudyTypeFromUrl();
      setFinalStudyType(parsedStudyType || StudyType.REINFORCEMENT_EXERCISE);
    }

    setIsClientReady(true);
  }, []);

  // 🔑 在客户端准备好之前，显示加载状态
  if (!isClientReady || !finalStudyType) {
    return (
      <div className="flex h-screen w-screen items-center justify-center bg-[var(--study-background)]">
        <Loading />
      </div>
    );
  }

  // 🔑 根据最终确定的 studyType 渲染对应组件
  switch (finalStudyType) {
    case StudyType.EXPAND_EXERCISE:
    case StudyType.REINFORCEMENT_EXERCISE:
      return <BaseExerciseEntry />;

    case StudyType.WRONG_QUESTION_BANK:
      return <WrongBookExerciseEntry />;
    default:
      return <></>;
  }
}

export default function ExercisePreviewPage() {
  useEffect(() => {
    setStatusBar({
      eventType: "setStatusBarVisibility",
      isVisible: false,
    });
  }, []);
  return (
    <Suspense
      fallback={
        <div className="flex h-screen w-screen items-center justify-center bg-[var(--study-background)]">
          <Loading />
        </div>
      }
    >
      <ClientProvider>
        <ExercisePageContent />
      </ClientProvider>
    </Suspense>
  );
}
