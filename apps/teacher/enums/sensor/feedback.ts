/**
 * 反馈模块
 */
export enum SensorFeedbackModule {
  LessonPart = "lesson_part", // 课时反馈
  LessonVideo = "lesson_video", // 视频反馈
  LessonQuestion = "lesson_question", // 题目反馈
  ExerciseQuestion = "exercise_question", // 练习反馈
  TaskReportQuestion = "task_report_question", // 任务报告反馈
  Global = "global", // 全局反馈
}

/**
 * 反馈埋点事件枚举
 */
export enum SensorFeedbackEvent {
  // 课时反馈事件
  LessonPartButtonClick = "lesson_feedback_part_button_click", // 课时反馈-点击反馈按钮
  LessonPartButtonSubmit = "lesson_feedback_part_button_submit", // 课时反馈-提交反馈内容

  // 视频反馈事件
  LessonVideoButtonClick = "lesson_feedback_video_button_click", // 视频反馈-点击反馈按钮
  LessonVideoButtonSubmit = "lesson_feedback_video_button_submit", // 视频反馈-提交反馈内容

  // 题目反馈事件
  LessonQuestionButtonClick = "lesson_feedback_question_button_click", // 题目反馈-点击反馈按钮
  LessonQuestionButtonSubmit = "lesson_feedback_question_button_submit", // 题目反馈-提交反馈内容

  // 练习反馈事件
  ExerciseQuestionButtonClick = "exercise_feedback_question_button_click", // 练习反馈-点击反馈按钮
  ExerciseQuestionButtonSubmit = "exercise_feedback_question_button_submit", // 练习反馈-提交反馈内容

  // 任务报告反馈事件
  TaskReportQuestionButtonClick = "task_report_feedback_question_button_click", // 任务报告反馈-点击反馈按钮
  TaskReportQuestionButtonSubmit = "task_report_feedback_question_button_submit", // 任务报告反馈-提交反馈内容

  // 全局反馈事件
  GlobalButtonClick = "global_feedback_button_click", // 全局反馈-点击反馈按钮
  GlobalButtonSubmit = "global_feedback_button_submit", // 全局反馈-提交反馈内容
}
