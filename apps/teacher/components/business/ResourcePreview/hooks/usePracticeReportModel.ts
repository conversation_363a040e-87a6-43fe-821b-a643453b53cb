import { FEEDBACK_TYPE, SensorFeedbackEvent } from "@/enums";
import { FeedbackSource, useFeedbackByType } from "@/hooks/useReportFeedback";
import { sensorsManager } from "@/libs/sensors";
import { CourseBaseInfo } from "@/types/assign/course";
import { QaContentType } from "@repo/core/views/tch-question-view/type";

export default function usePracticeReportModel(
  currentSubjectKey: number,
  treeNodeInfo?: CourseBaseInfo
) {
  const { routeToFeedback } = useFeedbackByType();

  const onReport = (question: QaContentType) => {
    if (!question) return;

    sensorsManager.track(SensorFeedbackEvent.ExerciseQuestionButtonClick, {
      question_id: question.questionId,
      question_version_id: question.questionVersionId,

      subject_id: currentSubjectKey,
      knowledge_id: treeNodeInfo?.bizTreeNodeId ?? -1,
    });

    routeToFeedback(FEEDBACK_TYPE.QUESTION, {
      feedbackSource: FeedbackSource.COURSE_PREVIEW,
      subjectId: currentSubjectKey,
      questionId: question.questionId,
      feedbackQuestionVersionId: question.questionVersionId,
      feedbackPhaseId: question.phase ?? -1,
    });
  };

  return {
    reportWidget: onReport,
  };
}
