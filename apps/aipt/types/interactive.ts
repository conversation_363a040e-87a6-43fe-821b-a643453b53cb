/**
 * 互动组件相关类型定义
 */

import { BaseWidget } from "./guide-widget";

/**
 * 互动组件数据结构
 */
export interface InteractiveData {
  /** 组件唯一标识 */
  id: string;
  /** 组件JS文件URL */
  url: string;
  /** 组件类型名称（自定义元素名） */
  typeName: string;
  /** 组件索引（0开始） */
  index: number;
  /** 组件显示序号（1开始） */
  widgetIndex: number;
  /** 组件名称（如"互动1"、"互动2"等） */
  widgetName?: string;
  /** 文件名 */
  fileName?: string;
  /** 组件来源类型 */
  sourceType?: "upload" | "ai_generated" | "template";
  /** 预览图URL */
  previewImage?: string;
}

/**
 * 互动组件管理器属性
 */
export interface InteractiveManagerProps {
  /** 排序变化回调 */
  onOrderChange?: (hasChanges: boolean) => void;
  /** 列表变化回调 */
  onListChange?: (interactiveList: InteractiveData[]) => void;
  /** 后端组件列表 */
  baseWidgetList?: BaseWidget[];
  /** Guide Widget ID */
  guideWidgetId?: number;
  /** Guide Widget Set ID */
  guideWidgetSetId?: number;
  /** 刷新数据回调 */
  onRefresh?: () => void;
}

/**
 * 文件上传限制配置
 */
export const FILE_UPLOAD_CONFIG = {
  /** 最大文件大小（字节） */
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  /** 允许的文件类型 */
  ALLOWED_TYPES: [".js", ".html"],
  /** 允许的MIME类型 */
  ALLOWED_MIME_TYPES: [
    "application/javascript",
    "text/javascript",
    "text/html",
  ],
  /** 最大组件数量 */
  MAX_COMPONENTS: 10,
} as const;

/**
 * 格式化文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};
