import { batch, useComputed, type Signal } from "@preact-signals/safe-react";
import { useCallback, useEffect } from "react";
import { IOption, ItemStatus } from "../components";
import { useExerciseContext } from "../context";
import { useQuestionViewContext } from "../context/question-view-context";
import { SelfEvaluateType } from "../enums";
import { Answer } from "../types";
import { getTreeOptionForPath } from "../utils";
import { isValidAnswerWithInputMode } from "../utils/question-answer";

export interface QuestionNavVM {
  /** 导航项（含 status），供视图渲染。只读 Signal，请在子组件里读取 .value */
  navOptions: Signal<IOption[]>;
}

const statusFromSelfEval: Record<SelfEvaluateType, ItemStatus> = {
  [SelfEvaluateType.pending]: ItemStatus.DEFAULT,
  [SelfEvaluateType.right]: ItemStatus.RIGHT,
  [SelfEvaluateType.wrong]: ItemStatus.WRONG,
  [SelfEvaluateType.partial]: ItemStatus.PARTIAL,
};

const getSingleQuestionBlankStatus = (data: {
  list: IOption[];
  answer?: Answer;
  targetIdx: number;
  isPreview?: boolean;
  isAnswerCompletedStatus: boolean;
}): {
  next: ItemStatus;
  prev: ItemStatus;
} => {
  const { list, answer, targetIdx, isPreview, isAnswerCompletedStatus } = data;

  if (!answer) return { next: ItemStatus.DEFAULT, prev: ItemStatus.DEFAULT };
  let next = ItemStatus.DEFAULT;
  const prev =
    getTreeOptionForPath(list, [targetIdx])?.status || ItemStatus.DEFAULT;

  const isAnswered = isPreview
    ? !!answer?.content
    : isValidAnswerWithInputMode(answer).isValid;

  // 非预览模式下-作答但未提交
  if (!isAnswerCompletedStatus && !isPreview) {
    next = isAnswered ? ItemStatus.ANSWERED : ItemStatus.DEFAULT;
  } else {
    // 预览模式或者非预览模式下已提交，比如正在自评
    next =
      statusFromSelfEval[answer?.selfEvaluation || SelfEvaluateType.pending];
  }

  return {
    next,
    prev,
  };
};
/**
 * 统一管理“是否作答/对错状态”的导航 VM：
 * - 监听 answers/verifyMap 等信号
 * - 仅在状态变化时，用 rAF 合并调用 updateTreeOptions
 * - 视图层只渲染 navOptions，不直接操作状态
 */
export function useQuestionNavVM(): QuestionNavVM {
  const { questionStore, isPreview } = useExerciseContext();
  const {
    isParentChildQuestion,
    currentQuestionAnswers,
    activeBlankIndex,
    currentQuestion,
    isAnswerCompletedStatus,
    answerVerifMap,
    activeQuestionIndex,
    userAnswerDataMap,
    allQuestionMetaMap,
    questionStatus,
    isSystemGradedQuestion,
    isSelfEvaluationQuestion,
  } = questionStore;

  const { updateTreeOptions, treeOptions } = useQuestionViewContext();

  // 更新非子母题的导航按钮状态
  const updateSingleQuestionBlankStatus = useCallback(
    ({ item, targetIdx }: { item?: Answer; targetIdx: number }) => {
      const list = treeOptions.value;

      const { next, prev } = getSingleQuestionBlankStatus({
        list,
        answer: item,
        targetIdx,
        isPreview,
        isAnswerCompletedStatus: isAnswerCompletedStatus.value,
      });

      if (prev === next) return;
      updateTreeOptions({
        // 在非预览模式下每次只更新当前空位的状态
        targetIdx,
        item: { status: next },
      });
    },
    [isPreview, isAnswerCompletedStatus, treeOptions, updateTreeOptions]
  );

  // 监听用户作答数据并更新非母子题的导航按钮状态
  useEffect(() => {
    if (isParentChildQuestion.value) {
      // const answers =
      //   userAnswerDataMap.value.get(currentQuestion.value?.questionId || "") ||
      //   [];
      console.log(
        222222,
        isSystemGradedQuestion.value,
        isSelfEvaluationQuestion.value,
        userAnswerDataMap.value
      );
      return;
    }
    const list = treeOptions.value;
    if (!list?.length) return;
    console.log(111111111, isPreview, isAnswerCompletedStatus.value);
    {
      const answers =
        userAnswerDataMap.value.get(currentQuestion.value?.questionId || "") ||
        [];

      // 非预览模式只更新当前空
      if (!isPreview && !isAnswerCompletedStatus.value) {
        if (!answers?.[activeBlankIndex.value]) return;
        updateSingleQuestionBlankStatus({
          item: answers?.[activeBlankIndex.value],
          targetIdx: activeBlankIndex.value,
        });
        return;
      }
      batch(() => {
        answers?.forEach((item, i) => {
          if (!item) return;
          updateSingleQuestionBlankStatus({
            item: item,
            targetIdx: i,
          });
        });
      });
    }
  }, [
    isParentChildQuestion,
    isAnswerCompletedStatus,
    isPreview,
    currentQuestion,
    userAnswerDataMap.value,
    treeOptions,
    activeBlankIndex,
    updateSingleQuestionBlankStatus,
  ]);

  /** 对外暴露：只读导航列表（给视图渲染即可） */
  const navOptions = useComputed(() => treeOptions.value);

  return { navOptions };
}
