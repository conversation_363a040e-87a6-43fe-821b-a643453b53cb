"use client";

import { StudyType } from "@repo/core/enums";
import { useCallback } from "react";
import { useExerciseContext } from "../context";
import { useExitStudySession } from "../models";

/**
 * 退出功能的 ViewModel
 *
 * 职责：
 * - 管理退出确认弹窗状态
 * - 处理不同学习类型的退出逻辑
 * - 调用退出会话API保存进度
 * - 提供统一的退出接口给View层
 *
 * 架构优势：
 * - View层无需直接调用Model层的hooks
 * - 退出逻辑集中管理，便于维护
 * - 支持不同学习类型的差异化处理
 */
export function useExitViewModel() {
  const { questionStore, clientContext, isPreview } = useExerciseContext();
  const {
    timerState,
    userAnswerDataMap,
    activeBlankIndex,
    activeQuestionIndex,
    exerciseParams,
    isShowExitConfirm,
    rootQuestion,
    isExiting,
    questionStatus,
  } = questionStore;

  const { widgetIndex, studySessionId, studyType, onBack } = exerciseParams;
  const { trackEvent } = clientContext;

  // 调用Model层的退出会话hook
  const { exitSession, exitError: apiExitError } = useExitStudySession();

  /**
   * 处理退出请求
   *
   * @param studyType 学习类型
   * @param onExitCallback 退出回调函数
   */
  const handleExitRequest = useCallback(() => {
    if (isPreview) {
      onBack?.();
      return;
    }
    // 清除之前的错误状态

    // 如果是AI课程，直接触发退出状态，不显示弹窗
    if (
      studyType === StudyType.AI_COURSE ||
      studyType === StudyType.WRONG_QUESTION_BANK
    ) {
      // AI课程直接返回，由外层处理退出逻辑
      onBack?.();
      return;
    }

    // 其他类型显示确认弹窗
    isShowExitConfirm.value = true;
    // 📊 埋点：显示退出确认弹窗
    if (rootQuestion.value?.questionId) {
      const showDialogParams = {
        study_session_id: studySessionId,
        question_id: rootQuestion.value?.questionId,
        study_type: studyType,
      };
      trackEvent?.("exercise_exit_confirm_click", showDialogParams);
    }
  }, [
    studyType,
    onBack,
    studySessionId,
    rootQuestion.value?.questionId,
    trackEvent,
    isShowExitConfirm,
    isPreview,
  ]);
  /**
   * 确认退出处理
   *
   */
  const handleConfirmExit = useCallback(async () => {
    isExiting.value = true;
    try {
      // 如果有学习会话ID，先调用退出会话接口
      if (studySessionId > 0 && rootQuestion.value?.questionId) {
        // 如果退出前是已提交状态，则不缓存，因为当前题目已经完成作答了，退出再进应该使用新的题目，不应该读缓存数据
        const cachedContent =
          questionStatus.value == "submitted"
            ? undefined
            : {
                questionStatus: questionStatus.value,
                timerState: timerState.peek(),
                questionId: rootQuestion.value?.questionId,
                userAnswerDataMap: Object.fromEntries(userAnswerDataMap.value),
                activeBlankIndex: activeBlankIndex.value,
                activeQuestionIndex: activeQuestionIndex.value,
              };

        await exitSession({
          studySessionId,
          questionId: rootQuestion.value?.questionId,
          widgetIndex: widgetIndex || undefined,
          answerDuration: timerState.peek().currentTime,
          cachedContent,
        }).finally(() => {
          isExiting.value = false;
        });
      }

      // 关闭确认弹窗
      isShowExitConfirm.value = false;

      // 触发退出回调
      if (onBack) {
        onBack();
      }
    } catch (error) {
      const exitErr =
        error instanceof Error ? error : new Error("退出会话失败");
      console.error("退出会话失败:", exitErr);

      isShowExitConfirm.value = false;

      // 即使退出会话失败，也要触发退出回调
      if (onBack) {
        onBack();
      }
    }
  }, [
    questionStatus,
    isExiting,
    exitSession,
    studySessionId,
    widgetIndex,
    userAnswerDataMap.value,
    activeBlankIndex.value,
    activeQuestionIndex.value,
    onBack,
    isShowExitConfirm,
    timerState,
    rootQuestion.value?.questionId,
  ]);

  /**
   * 取消退出
   */
  const handleCancelExit = useCallback(() => {
    isShowExitConfirm.value = false;
  }, []);

  return {
    // 状态
    isShowExitConfirm,
    isExiting,

    // 操作
    handleExitRequest,
    handleConfirmExit,
    handleCancelExit,
  };
}
