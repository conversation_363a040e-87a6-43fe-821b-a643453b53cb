# New-Exercise ViewModels 视图模型层

新版答题系统的视图模型层，基于 Context-First 零参数架构设计，负责业务逻辑封装、状态管理和数据转换，作为View层和Model层之间的桥梁。

## 📁 文件结构

```
viewmodels/
├── question-core-vm.ts         # 核心题目业务逻辑
├── choice-question-vm.ts       # 选择题专用ViewModel
├── submission-vm.ts            # 答案提交ViewModel（Context-First架构）
├── confirm-vm.ts               # 确认对话框ViewModel
├── exit-viewmodel.ts           # 退出功能ViewModel
├── feedback-viewmodel.ts       # 反馈收集ViewModel
├── question-track-vm.ts        # 埋点追踪ViewModel
├── timer-vm.ts                 # 计时器ViewModel
├── animation-transition-vm.tsx # 转场动画ViewModel
├── preview-viewmodel.ts        # 预览模式ViewModel
├── README.md                   # 说明文档
└── index.ts                    # 统一导出
```

## 🏗️ 架构设计

### Context-First MVVM架构
```mermaid
graph TD
    A[View 视图层] --> B[Context 上下文层]
    B --> C[ViewModel 视图模型层]
    C --> D[Signal Store 状态层]
    C --> E[Model API层]

    F[Context-First 零参数] --> C
    G[Signal 响应式状态] --> D
    H[策略模式验证] --> C
    I[统一错误处理] --> C
```

### 设计原则
1. **Context-First 架构** - ViewModel 通过 Context 获取所有数据，实现零参数传递
2. **Signal 响应式状态** - 基于 Preact Signals 的细粒度状态管理
3. **策略模式验证** - 使用策略模式处理不同题型的校验逻辑
4. **统一错误处理** - 完善的错误处理和用户反馈机制
5. **高性能优化** - 避免不必要的重渲染和状态更新
6. **后端控制重试** - 使用 `SubmitAnswerResponse.isFinalAnswer` 由后端控制是否允许重复作答，前端不再维护提交次数计数器

## 🔧 核心ViewModels

### useQuestionSubmissionVM - Context-First 答案提交逻辑
基于 Context-First 零参数架构的答案提交 ViewModel，统一处理所有题型的答案提交，包括白板作答的自动上传：

```typescript
/**
 * ✅ Context-First 题目提交 Hook（零参数架构）
 *
 * 通过 useExerciseContext() 获取所有必要数据：
 * - 题目信息、状态、用户答案
 * - ViewModel 实例和方法
 * - 提交相关的配置和回调
 */
export function useQuestionSubmissionVM() {
  // ✅ 通过 Context 获取所有必要数据
  const { questionStore, studyType, studySessionId, widgetIndex } =
    useExerciseContext();
  const { trackEventWithExercise } = useQuestionTrackVM();
  const {
    timerState,
    currentStrategy,
    progressBarState,
    isSubmitting,
    questionStatus,
    currentQuestion,
    updateQuestionStatus,
    questionRenderType,
    lastSubmitResult,
    streakCount,
    initialProgress,
    isChoiceQuestion,
    getBaseTrackParams,
    userAnswerDataMap,
    userAnswerTypeMap,
    rootQuestion,
    canSubmitSelfEvaluation,
  } = questionStore;

  // 🔥 使用 useProgressBar hook 统一管理进度条状态和动画
  const { handleProgress } = useProgressBar({
    initialProgress: initialProgress.value,
  });

  // 🔧 使用弹窗 Hook
  const { showConfirmDialogAsync } = useConfirmDialogVM();

  /**
   * ✅ Hook 完全主导：拥有所有业务逻辑
   *
   * 完整的业务逻辑流程：
   * 1. 策略调用和校验
   * 2. 弹窗处理
   * 3. 白板数据上传确保
   * 4. API 调用
   * 4. 状态更新（questionState 计算）
   * 5. 埋点
   * 6. 进度处理
   */
  const beforeSubmitAnswer = async (): Promise<SubmissionResult> => {
    try {
      if (!currentQuestion.value) {
        return { success: false, error: new Error("当前题目信息不存在") };
      }

      // 1. ✅ 防重复提交检查
      if (isSubmitting.value) {
        return { success: false, error: new Error("正在提交中") };
      }

      // 2. ✅ 策略调用和校验
      if (!currentStrategy.value) {
        return {
          success: false,
          error: new Error(`未找到题型 ${questionRenderType} 对应的策略`),
        };
      }

      const strategyContext = {
        questionStatus: questionStatus.value,
        userAnswerData: userAnswerDataMap.value,
      };

      // 策略验证从统一状态获取数据
      const validation = currentStrategy.value.validateBeforeSubmit(
        currentQuestion.value,
        strategyContext
      );

      // 3. ✅ 弹窗处理
      if (validation.needsConfirmation) {
        const confirmed = await showConfirmDialogAsync(
          validation.confirmConfig
        );

        if (!confirmed) {
          return { success: false, cancelled: true };
        }
      }

      // 4. ✅ 确保白板数据已上传
      try {
        await uploadAllWhiteboardData();
      } catch (error) {
        console.warn("白板数据上传失败，但不阻塞提交:", error);
        // 白板上传失败不阻塞提交流程
      }

      await submitAnswer();
      return { success: true, result: null };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error : new Error(String(error)),
      };
    }
  };

  return {
    canSubmitSelfEvaluation,
    beforeSubmitAnswer,
    submitAnswer,
    giveUpAnswer,
    handleSubmitSelfEvaluation,
  };
}
```

#### 核心功能
1. **Context-First 架构** - 零参数传递，通过 Context 获取所有数据
2. **策略模式验证** - 使用策略模式验证不同题型的答案
3. **统一提交流程** - 处理普通提交、放弃作答、自评提交
4. **进度条管理** - 集成进度条动画和状态更新
5. **埋点追踪** - 自动记录提交相关埋点事件
6. **错误处理** - 完善的错误处理和用户反馈

### useChoiceQuestionViewModel - 选择题专用逻辑
专门处理选择题的交互逻辑，支持母子题导航：

```typescript
export function useChoiceQuestionViewModel() {
  // 🎯 从统一Context获取题目数据和答案管理方法
  const { questionStore } = useExerciseContext();
  const {
    questionStatus,
    updateUserAnswer,
    currentQuestionAnswers,
    isMultipleChoiceQuestion,
  } = questionStore;

  // 🆕 获取当前激活的题目索引
  const { activeBlankIndex, currentActiveQuestion } = useQuestionViewContext();

  // 处理选项点击
  const handleOptionClick = useCallback((optionKey: string) => {
    if (questionStatus.value === "uncertain" || questionStatus.value === "second_answering") {
      updateQuestionStatus("answering");
    }

    if (!currentActiveQuestion) return;

    const currentChoices = currentQuestionAnswers.value;
    let newSelectedOptions: string[];

    if (isMultipleChoiceQuestion.value) {
      // 多选题逻辑 - 🆕 使用当前激活的题目索引
      const currentContent =
        (currentChoices?.[activeBlankIndex]?.content || undefined)?.split(",") || [];

      if (currentContent.includes(optionKey)) {
        newSelectedOptions = currentContent.filter(answer => answer !== optionKey);
      } else {
        newSelectedOptions = [...currentContent, optionKey];
      }
    } else {
      // 单选题逻辑
      newSelectedOptions = [optionKey];
    }

    const content = newSelectedOptions?.length ? newSelectedOptions.join(",") : "";

    // 🔧 更新Context中的答案状态
    updateUserAnswer({
      content,
      questionId: currentActiveQuestion.questionId,
      // 🆕 使用当前激活的题目索引，支持母子题导航
      blankIndex: activeBlankIndex,
    });
  }, [/* dependencies */]);

  return {
    currentQuestionAnswers,
    isMultipleChoiceQuestion,
    hasSelection,
    handleOptionClick,
  };
}
```

#### 核心功能
1. **母子题导航支持** - 使用 activeBlankIndex 支持母子题统一寻址
2. **选项状态管理** - 管理单选/多选的选择状态
3. **交互逻辑** - 处理选项点击和状态切换
4. **答案同步** - 与统一答案状态同步

## 🎨 白板作答提交流程

### 白板数据流程设计
白板作答采用增量上传策略，避免提交时集中处理导致的性能问题：

```mermaid
graph TD
    A[用户在白板绘画] --> B[离开空/子题]
    B --> C[检查数据变化]
    C --> D{数据是否变化?}
    D -->|是| E[异步上传图片]
    D -->|否| F[跳过上传]
    E --> G[更新 content 字段]
    G --> H[同步到 userAnswerDataMap]
    F --> I[提交前检查]
    H --> I
    I --> J[重试失败的上传]
    J --> K[正常提交流程]
```

### 集成点说明

#### 1. 提交前白板数据确保
在 `useQuestionSubmissionVM.beforeSubmitAnswer` 中集成：

```typescript
// 4. ✅ 确保白板数据已上传
try {
  await uploadAllWhiteboardData();
} catch (error) {
  console.warn("白板数据上传失败，但不阻塞提交:", error);
  // 白板上传失败不阻塞提交流程
}
```

#### 2. 白板组件离开时保存
白板组件需要在离开时调用保存方法：

```typescript
const { saveWhiteboardData } = useWhiteboardAnswer();

useEffect(() => {
  return () => {
    if (currentQuestionId && currentBlankIndex !== null && whiteboardData) {
      saveWhiteboardData(currentQuestionId, currentBlankIndex, whiteboardData);
    }
  };
}, [currentQuestionId, currentBlankIndex, whiteboardData]);
```

#### 3. 容错处理
- 上传失败不阻塞用户操作
- 提交时重试失败的上传
- 错误日志记录但不影响主流程

### useConfirmDialogVM - 确认对话框管理
基于 Signal Store 的确认对话框管理，支持异步确认：

```typescript
/**
 * 确认弹窗 Hook
 *
 * 提供弹窗状态管理和确认逻辑
 */
export function useConfirmDialogVM() {
  const { questionStore } = useExerciseContext();
  const { confirmDialogState } = questionStore;

  /**
   * 显示确认弹窗并等待用户确认
   *
   * @param config 弹窗参数
   * @returns Promise<boolean> 用户是否确认
   */
  const showConfirmDialogAsync = useCallback(
    async (config?: ConfirmDialogConfigType): Promise<boolean> => {
      return new Promise((resolve) => {
        // 创建弹窗配置
        const dialogConfig: ConfirmDialogConfig = {
          isVisible: true,
          ...config,
          onClose: () => {
            hideConfirmDialog();
            resolve(false);
          },
          onConfirm: () => {
            hideConfirmDialog();
            resolve(true);
          },
          onCancel: () => {
            hideConfirmDialog();
            resolve(false);
          },
        };

        // 显示弹窗
        showConfirmDialog(dialogConfig);
      });
    },
    [showConfirmDialog, hideConfirmDialog]
  );

  return {
    confirmDialogState,
    showConfirmDialog,
    hideConfirmDialog,
    showConfirmDialogAsync,
  };
}
```

#### 核心功能
1. **Signal Store 集成** - 基于 Signal 的状态管理
2. **异步确认** - 支持 async/await 的确认流程
3. **类型安全** - 完整的 TypeScript 类型定义
4. **统一接口** - 为所有确认场景提供统一接口

### useQuestionTrackVM - 埋点追踪管理
统一管理答题相关的埋点事件，自动处理基础参数：

```typescript
export function useQuestionTrackVM() {
  const { questionStore } = useExerciseContext();
  const { trackEvent, surveillanceReport } = useClient();
  const { currentQuestion, getBaseTrackParams } = questionStore;

  // 🆕 埋点方法：自动处理练习相关参数
  const trackEventWithExercise = useCallback(
    (eventName: string, additionalParams: Record<string, unknown> = {}) => {
      const baseParams = getBaseTrackParams();
      const currentQuestionId = baseParams?.question_id;

      if (!currentQuestionId) {
        console.warn(
          "[QuestionContext] trackEventWithExercise: 当前没有题目ID，跳过埋点"
        );
        return;
      }

      const finalParams = {
        ...baseParams,
        ...additionalParams,
      };

      trackEvent?.(eventName, finalParams);
    },
    [getBaseTrackParams, trackEvent]
  );

  const trackEventWithFeedback = useCallback(
    (result: SubmitAnswerResponse, isGiveUp?: boolean) => {
      const isCorrect = result.answerResult?.every(
        (item) => item.answerVerify === AnswerVerifyType.CORRECT
      );

      const baseParams = getBaseTrackParams();

      // 📊 埋点：答题反馈相关
      const feedbackParams = {
        ...baseParams,
        feedback_type: isGiveUp
          ? AnswerFeedbackType.GiveUp
          : isCorrect
            ? "correct"
            : "incorrect",
        feedback_content: result.feedback?.content || "",
        answer_result: isCorrect ? "correct" : "incorrect",
        is_giveup: isGiveUp || false,
      };

      trackEvent?.("exercise_feedback_show", feedbackParams);

      // 📊 埋点：连胜反馈
      if (result.correctComboCount && Number(result?.correctComboCount) > 1) {
        const streakFeedbackParams = {
          ...baseParams,
          feedback_type: "winning_streak",
          streak_count: Number(result?.correctComboCount) || 0,
          feedback_content: result.feedback.content,
        };
        trackEvent?.("winning_streak_feedback_show", streakFeedbackParams);
      }
    },
    [getBaseTrackParams, currentQuestion.value, trackEvent, surveillanceReport]
  );

  return {
    trackEventWithFeedback,
    trackEventWithExercise,
  };
}
```

#### 核心功能
1. **自动参数处理** - 自动添加基础埋点参数
2. **反馈埋点** - 专门处理答题反馈相关埋点
3. **连胜埋点** - 处理连胜反馈的特殊埋点
4. **错误处理** - 当缺少必要参数时跳过埋点

## 🔄 与其他层集成

### Context-First 架构集成
ViewModel 层通过 Context 层实现零参数架构，所有数据通过 Context 获取：

```typescript
/**
 * 统一的题目上下文提供者
 *
 * ✅ 集成了 ViewModel 的完整功能
 * ✅ 类型安全且代码简洁
 * ✅ 性能优化的状态管理
 */
export function ExerciseContextProvider({
  questionStore,
  children,
}: ExerciseContextProviderProps) {
  const { exerciseParams } = questionStore;

  // ===== 🔧 ViewModel 集成 =====
  const { handleContinue, handleContinueWithTransitions, nextTransition } =
    useQuestionViewModel({
      questionStore,
      exerciseParams,
    });

  // ===== 🔧 Context 值构建 =====
  const contextValue: QuestionContextValue = useMemo(
    () => ({
      handleContinue,
      handleContinueWithTransitions,
      nextTransition,
      questionStore,
      ...exerciseParams,
    }),
    [
      handleContinue,
      handleContinueWithTransitions,
      questionStore,
      exerciseParams,
      nextTransition,
    ]
  );

  return (
    <ExerciseQuestionContext.Provider value={contextValue}>
      {children}
    </ExerciseQuestionContext.Provider>
  );
}
```

### Signal Store 集成
ViewModel 层基于 Preact Signals 实现响应式状态管理：

```typescript
export const useQuestionStore = (exerciseParams: ExerciseInitParams) => {
  // Signal 状态定义
  const currentQuestion = useSignal<StudyQuestionInfo | null>(null);
  const questionStatus = useSignal<QuestionStatus>("answering");
  const isSubmitting = useSignal<boolean>(false);
  const userAnswerDataMap = useSignal<Map<string, Answer[]>>(new Map());

  // 计算属性
  const isChoiceQuestion = useComputed(() => {
    return isChoiceQuestionType(currentQuestion.value);
  });

  const canSubmitSelfEvaluation = useComputed(() => {
    if (!isSelfEvaluationQuestion.value) return false;

    const answers = userAnswerDataMap.value.get(currentQuestion.value?.questionId || "");
    return answers?.every(answer =>
      answer.selfEvaluation !== undefined && answer.selfEvaluation > 0
    ) || false;
  });

  // 状态更新方法
  const updateUserAnswer = (state: UpdateStudentAnswerParams) => {
    const { questionId, blankIndex, type = InputModeType.KEYBOARD, content } = state;

    updateUserAnswerType(questionId, type);

    if (questionStatus.value === "giving_up" || questionStatus.value === "uncertain") {
      updateQuestionStatus("answering");
    }

    const allAnswerData = new Map(userAnswerDataMap.value);
    const cloneCurrentAnswers = [...(allAnswerData.get(questionId) || [])];

    cloneCurrentAnswers[blankIndex] = {
      index: blankIndex,
      content: content,
    };

    allAnswerData.set(questionId, cloneCurrentAnswers);
    userAnswerDataMap.value = allAnswerData;
  };

  return {
    // Signal 状态
    currentQuestion,
    questionStatus,
    isSubmitting,
    userAnswerDataMap,

    // 计算属性
    isChoiceQuestion,
    canSubmitSelfEvaluation,

    // 状态更新方法
    updateUserAnswer,
    updateQuestionStatus,
  };
};
```

### Model 层集成
ViewModel 层调用 Model 层的 API 进行数据操作：

```typescript
export function useQuestionSubmissionVM() {
  // 使用 Model 层的 API Hook
  const { submitAnswer: fetchSubmitAnswer } = useSubmitStudyAnswer();

  const submitAnswer = useCallback(
    async (isGiveUp = false): Promise<SubmissionResult> => {
      try {
        // 构建提交数据
        const studentAnswers: SubmitAnswerContent[] = isGiveUp
          ? []
          : buildSubmissionData({
            userAnswerData: userAnswerDataMap.value,
            userAnswerTypeMap: userAnswerTypeMap.value,
          });

        if (!currentQuestion.value?.questionId) {
          return { success: false, error: new Error("题目ID不存在") };
        }

        isSubmitting.value = true;

        // 调用 Model 层 API
        const result = await fetchSubmitAnswer({
          ...submitApiParams.value,
          questionId: currentQuestion.value.questionId,
          studentAnswers: studentAnswers,
          isGiveup: isGiveUp,
        });

        isSubmitting.value = false;

        // 处理提交结果
        afterSubmitAnswer({ result, isGiveUp, studentAnswers });

        return { success: true, result };
      } catch (error) {
        isSubmitting.value = false;
        return {
          success: false,
          error: error instanceof Error ? error : new Error(String(error)),
        };
      }
    },
    [/* dependencies */]
  );
}
```

## 🚀 使用示例

### Context-First 基础使用
```typescript
// Context-First 架构：组件通过 Context 获取所有数据
const QuestionComponent = () => {
  const {
    questionStore: {
      currentQuestion,
      questionStatus,
      userAnswerDataMap,
    }
  } = useExerciseContext(); // 零参数，通过 Context 获取所有数据

  return (
    <div className="question-component">
      <QuestionView question={currentQuestion.value} />
      <AnswerArea
        answers={userAnswerDataMap.value.get(currentQuestion.value?.questionId || '') || []}
      />
      <QuestionActionButtons />
    </div>
  );
};
```

### 提交 ViewModel 使用
```typescript
const SubmitButtonComponent = () => {
  // Context-First：零参数获取提交功能
  const { beforeSubmitAnswer, canSubmitSelfEvaluation } = useQuestionSubmissionVM();
  const { questionStore: { isSubmitting, questionStatus } } = useExerciseContext();

  const handleSubmit = async () => {
    const result = await beforeSubmitAnswer();

    if (result.success) {
      console.log('提交成功');
    } else if (result.cancelled) {
      console.log('用户取消提交');
    } else {
      console.error('提交失败:', result.error);
    }
  };

  const canSubmit = useMemo(() => {
    return questionStatus.value === 'answering' && !isSubmitting.value;
  }, [questionStatus.value, isSubmitting.value]);

  return (
    <button
      onClick={handleSubmit}
      disabled={!canSubmit}
      className={cn(
        "submit-button",
        isSubmitting.value && "loading"
      )}
    >
      {isSubmitting.value ? '提交中...' : '提交答案'}
    </button>
  );
};
```

### 选择题 ViewModel 使用
```typescript
const ChoiceQuestionComponent = () => {
  // Context-First：零参数获取选择题功能
  const {
    currentQuestionAnswers,
    isMultipleChoiceQuestion,
    hasSelection,
    handleOptionClick,
  } = useChoiceQuestionViewModel();

  const { questionStore: { currentQuestion } } = useExerciseContext();
  const { activeBlankIndex } = useQuestionViewContext();

  return (
    <div className="choice-question-component">
      {currentQuestion.value?.questionAnswer?.answerOptionList?.map?.((option) => (
        <button
          key={option.optionKey}
          onClick={() => handleOptionClick(option.optionKey)}
          className={cn(
            "option-button",
            currentQuestionAnswers.value?.[activeBlankIndex]?.content
              ?.split(',')
              ?.includes(option.optionKey) && "selected"
          )}
        >
          {option.optionKey}. {option.optionVal}
        </button>
      ))}

    </div>
  );
};
```

### 确认对话框 ViewModel 使用
```typescript
const ExerciseContainer = () => {
  // Context-First：零参数获取确认对话框功能
  const { showConfirmDialogAsync } = useConfirmDialogVM();

  const handleRiskyAction = async () => {
    const confirmed = await showConfirmDialogAsync({
      type: 'multipleChoiceConfirm',
      title: '确认提交',
      message: '您只选择了一个选项，确定要提交吗？',
      confirmText: '确定提交',
      cancelText: '继续选择',
    });

    if (confirmed) {
      // 执行提交操作
      performSubmitAction();
    }
  };

  return (
    <div className="exercise-container">
      <button onClick={handleRiskyAction} className="action-button">
        提交答案
      </button>
    </div>
  );
};
```

### 埋点追踪 ViewModel 使用
```typescript
const QuestionInteractionComponent = () => {
  // Context-First：零参数获取埋点功能
  const { trackEventWithExercise } = useQuestionTrackVM();

  const handleOptionClick = (optionKey: string) => {
    // 自动埋点：选项点击
    trackEventWithExercise('exercise_option_click', {
      option_key: optionKey,
      click_timestamp: Date.now(),
    });

    // 处理选项点击逻辑
    // ...
  };

  const handleAnswerChange = (content: string) => {
    // 自动埋点：答案变更
    trackEventWithExercise('exercise_answer_change', {
      answer_length: content.length,
      change_timestamp: Date.now(),
    });

    // 处理答案变更逻辑
    // ...
  };

  return (
    <div className="question-interaction">
      {/* 组件内容 */}
    </div>
  );
};
```

## 🎯 架构优势

### 1. Context-First 零参数架构
```mermaid
graph LR
    A[Context Provider] --> B[统一数据源]
    B --> C[ViewModel 零参数]
    C --> D[业务逻辑封装]

    E[View 组件] --> F[useExerciseContext]
    F --> G[获取所有数据]
    G --> H[无需参数传递]
```

**优势：**
- **零参数传递** - ViewModel 通过 Context 获取所有数据，无需参数传递
- **类型安全** - Context 提供完整类型定义和自动推导
- **性能优化** - Context 使用 useMemo 优化，避免不必要重渲染
- **符合 React 最佳实践** - 正确使用 Context 模式

### 2. Signal 响应式状态管理
```typescript
// ✅ 基于 Preact Signals 的响应式状态
const currentQuestion = useSignal<StudyQuestionInfo | null>(null);
const questionStatus = useSignal<QuestionStatus>("answering");

// 自动计算属性，只在依赖变化时重新计算
const isChoiceQuestion = useComputed(() => {
  return isChoiceQuestionType(currentQuestion.value);
});

// 组件自动订阅状态变化
const QuestionComponent = () => {
  // 只有 currentQuestion 变化时才重渲染
  const question = currentQuestion.value;
  return <div>{question?.questionId}</div>;
};
```

**优势：**
- **细粒度更新** - 只有依赖的 Signal 变化时组件才重渲染
- **自动依赖追踪** - 无需手动管理依赖数组
- **性能优化** - 避免不必要的重渲染和计算
- **简化状态管理** - 减少 useState 和 useEffect 的使用

### 3. 策略模式验证
```typescript
// ✅ 策略模式处理不同题型的验证逻辑
export function useQuestionSubmissionVM() {
  const { currentStrategy } = questionStore;

  const beforeSubmitAnswer = async () => {
    // 获取当前题型对应的策略
    const strategy = currentStrategy.value;

    // 策略验证
    const validation = strategy.validateBeforeSubmit(
      currentQuestion.value,
      { questionStatus: questionStatus.value, userAnswerData: userAnswerDataMap.value }
    );

    // 根据验证结果处理
    if (validation.needsConfirmation) {
      const confirmed = await showConfirmDialogAsync(validation.confirmConfig);
      if (!confirmed) return { success: false, cancelled: true };
    }

    return await submitAnswer();
  };
}
```

**优势：**
- **可扩展性** - 新增题型只需添加对应策略
- **代码复用** - 相同验证逻辑可在多个策略中复用
- **维护性** - 每个策略独立，便于维护和测试
- **类型安全** - 策略接口保证类型一致性

### 4. 统一错误处理
```typescript
// ViewModel 层统一错误处理
export function useQuestionSubmissionVM() {
  const submitAnswer = useCallback(
    async (isGiveUp = false): Promise<SubmissionResult> => {
      try {
        // 业务逻辑
        const result = await fetchSubmitAnswer({...});
        return { success: true, result };
      } catch (error) {
        console.error("[useQuestionSubmissionVM] 提交失败:", error);

        // 统一错误处理
        return {
          success: false,
          error: error instanceof Error ? error : new Error(String(error)),
        };
      }
    },
    [/* dependencies */]
  );
}
```

**优势：**
- **统一接口** - 所有 ViewModel 方法返回统一的结果格式
- **错误类型化** - 错误信息类型安全，便于处理
- **调试友好** - 统一的错误日志格式
- **用户体验** - 一致的错误反馈机制

## ⚡ 性能优化

### 1. Signal 响应式优化
```typescript
// ✅ 使用 Signal 实现细粒度更新
const currentQuestion = useSignal<StudyQuestionInfo | null>(null);
const questionStatus = useSignal<QuestionStatus>("answering");

// 计算属性自动缓存，只在依赖变化时重新计算
const isChoiceQuestion = useComputed(() => {
  return isChoiceQuestionType(currentQuestion.value);
});

// 组件只在相关 Signal 变化时重渲染
const QuestionStatusDisplay = () => {
  // 只订阅 questionStatus，不会因为其他状态变化而重渲染
  return <div>状态: {questionStatus.value}</div>;
};
```

### 2. Context 优化策略
```typescript
// ✅ 使用 useMemo 优化 Context 值
const contextValue: QuestionContextValue = useMemo(
  () => ({
    handleContinue,
    handleContinueWithTransitions,
    nextTransition,
    questionStore,
    ...exerciseParams,
  }),
  [
    handleContinue,
    handleContinueWithTransitions,
    questionStore,
    exerciseParams,
    nextTransition,
  ]
);

// ✅ 分离高频和低频状态
const TimerContext = createContext(timerState); // 高频更新
const QuestionContext = createContext(questionData); // 低频更新
```

### 3. 避免循环依赖
```typescript
// ✅ 使用 peek() 避免依赖追踪
const submitApiParams = useComputed(() => ({
  studySessionId: studySessionId || 0,
  questionId: rootQuestion.peek()?.questionId, // 使用 peek() 避免依赖追踪
  answerDuration: timerState.peek().currentTime, // 使用 peek() 避免依赖追踪
  ...((studyType || StudyType.AI_COURSE) === StudyType.AI_COURSE &&
    widgetIndex !== undefined && {
    widgetIndex: widgetIndex,
  }),
}));

// ✅ 最小化依赖数组
const handleOptionClick = useCallback((optionKey: string) => {
  // 处理逻辑，只依赖必要的状态
}, [activeBlankIndex, currentActiveQuestion, updateUserAnswer]);
```

### 4. 内存管理和清理
```typescript
// ✅ 及时清理状态和订阅
useEffect(() => {
  return () => {
    // 清理定时器
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    // 清理文件引用
    imageFiles.forEach(file => {
      if (file.url && file.url.startsWith('blob:')) {
        URL.revokeObjectURL(file.url);
      }
    });

    // 重置状态
    isSubmitting.value = false;
    confirmDialogState.value = { isVisible: false };
  };
}, []);

// ✅ 使用 batch 进行批量状态更新
import { batch } from '@preact/signals-core';

const handleSubmitComplete = (result: SubmitAnswerResponse) => {
  batch(() => {
    isSubmitting.value = false;
    lastSubmitResult.value = result;
    if (result.nextQuestionInfo) {
      currentQuestion.value = result.nextQuestionInfo;
    }
  });
};
```

## 🧪 测试策略

### Context-First ViewModel 测试
```typescript
describe('useQuestionSubmissionVM', () => {
  const mockQuestionStore = {
    currentQuestion: signal(mockQuestion),
    questionStatus: signal('answering' as QuestionStatus),
    isSubmitting: signal(false),
    userAnswerDataMap: signal(new Map()),
    currentStrategy: signal(mockStrategy),
    getBaseTrackParams: () => ({ question_id: 'test-123' }),
  };

  const TestWrapper = ({ children }: { children: React.ReactNode }) => (
    <ExerciseContextProvider questionStore={mockQuestionStore}>
      <QuestionViewProvider>
        {children}
      </QuestionViewProvider>
    </ExerciseContextProvider>
  );

  beforeEach(() => {
    jest.clearAllMocks();
    // 重置 Signal 状态
    mockQuestionStore.isSubmitting.value = false;
    mockQuestionStore.questionStatus.value = 'answering';
  });

  test('should handle Context-First submission flow', async () => {
    const { result } = renderHook(() => useQuestionSubmissionVM(), {
      wrapper: TestWrapper,
    });

    // 测试零参数调用
    await act(async () => {
      const submissionResult = await result.current.beforeSubmitAnswer();
      expect(submissionResult.success).toBe(true);
    });

    // 验证状态更新
    expect(mockQuestionStore.isSubmitting.value).toBe(false);
  });

  test('should handle validation with confirmation', async () => {
    // Mock 策略返回需要确认
    const mockValidation = {
      needsConfirmation: true,
      confirmConfig: {
        type: 'multipleChoiceConfirm',
        title: '确认提交',
        message: '只选择了一个选项',
      },
    };

    mockQuestionStore.currentStrategy.value = {
      validateBeforeSubmit: jest.fn().mockReturnValue(mockValidation),
    };

    const { result } = renderHook(() => useQuestionSubmissionVM(), {
      wrapper: TestWrapper,
    });

    // Mock 用户取消确认
    const mockShowConfirmDialogAsync = jest.fn().mockResolvedValue(false);
    jest.spyOn(require('./confirm-vm'), 'useConfirmDialogVM').mockReturnValue({
      showConfirmDialogAsync: mockShowConfirmDialogAsync,
    });

    await act(async () => {
      const result = await result.current.beforeSubmitAnswer();
      expect(result.cancelled).toBe(true);
    });
  });
});
```

### Signal Store 测试
```typescript
describe('useQuestionStore', () => {
  const mockExerciseParams = {
    studySessionId: 123,
    studyType: StudyType.REINFORCEMENT_EXERCISE,
  };

  test('should update user answer correctly', () => {
    const store = useQuestionStore(mockExerciseParams);

    // 设置当前题目
    store.currentQuestion.value = mockQuestion;

    // 更新答案
    act(() => {
      store.updateUserAnswer({
        questionId: mockQuestion.questionId,
        blankIndex: 0,
        content: 'A',
        type: InputModeType.KEYBOARD,
      });
    });

    // 验证答案状态
    const answers = store.userAnswerDataMap.value.get(mockQuestion.questionId);
    expect(answers?.[0]).toEqual({
      index: 0,
      content: 'A',
    });
  });

  test('should compute properties correctly', () => {
    const store = useQuestionStore(mockExerciseParams);

    // 设置选择题
    store.currentQuestion.value = {
      ...mockQuestion,
      questionAnswerMode: QuestionAnswerMode.SINGLE_CHOICE,
    };

    // 验证计算属性
    expect(store.isChoiceQuestion.value).toBe(true);
    expect(store.isMultipleChoiceQuestion.value).toBe(false);
  });
});
```

### 集成测试
```typescript
describe('Question Submission Integration', () => {
  test('should complete full Context-First submission flow', async () => {
    const mockSubmitAnswer = jest.fn().mockResolvedValue({
      answerResult: [{ answerVerify: AnswerVerifyType.CORRECT, index: 0, questionId: 'test' }],
      hasNextQuestion: true,
      nextQuestionInfo: mockNextQuestion,
      feedback: { content: '回答正确！' },
    });

    // Mock Model 层
    jest.spyOn(require('../models/exercise-model'), 'useSubmitStudyAnswer').mockReturnValue({
      submitAnswer: mockSubmitAnswer,
    });

    const { result } = renderHook(() => useQuestionSubmissionVM(), {
      wrapper: TestWrapper,
    });

    await act(async () => {
      const submissionResult = await result.current.beforeSubmitAnswer();
      expect(submissionResult.success).toBe(true);
    });

    expect(mockSubmitAnswer).toHaveBeenCalled();
  });
});
```

## 🔧 维护规范

### Context-First ViewModel 设计原则
1. **零参数架构** - ViewModel 通过 Context 获取所有数据，避免参数传递
2. **Signal 响应式** - 基于 Preact Signals 实现细粒度状态管理
3. **策略模式** - 使用策略模式处理不同题型的业务逻辑
4. **统一错误处理** - 所有方法返回统一的结果格式
5. **类型安全** - 完整的 TypeScript 类型定义和推导

### 新增 ViewModel 步骤
1. **确定职责范围** - 明确 ViewModel 负责的业务领域
2. **设计 Context 接口** - 定义需要从 Context 获取的数据
3. **实现核心逻辑** - 基于 Context-First 架构实现业务逻辑
4. **集成 Signal Store** - 确保与 Signal Store 正确集成
5. **编写单元测试** - 测试零参数调用和状态管理
6. **更新文档** - 更新 README 和接口文档

### 代码质量要求
- **Context-First 原则** - 所有数据通过 Context 获取，避免参数传递
- **Signal 优化** - 正确使用 useComputed 和 peek() 避免循环依赖
- **错误处理** - 返回统一的 SubmissionResult 格式
- **性能优化** - 使用 useCallback 和最小化依赖数组
- **类型安全** - 完整的 TypeScript 类型定义

### 最佳实践
```typescript
// ✅ 正确的 Context-First ViewModel
export function useMyViewModel() {
  // 通过 Context 获取所有数据
  const { questionStore } = useExerciseContext();
  const { currentQuestion, updateState } = questionStore;

  // 使用 useCallback 优化性能
  const handleAction = useCallback(async (): Promise<ActionResult> => {
    try {
      // 业务逻辑
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error : new Error(String(error))
      };
    }
  }, [/* 最小化依赖 */]);

  return { handleAction };
}

// ❌ 错误的参数传递方式
export function useMyViewModel(question: Question, onUpdate: Function) {
  // 不要通过参数传递数据
}
```

## 🆕 母子题导航系统集成

### 统一答案寻址
所有 ViewModel 现在使用统一的叶子索引系统，支持任意题型与两层母子题：

```typescript
// ❌ 旧方式（写死 index=0）
updateUserAnswer({
  content: newSelectedOptions,
  questionId: displayQuestion.questionId,
  index: 0, // 写死，不支持母子题
  type: 1,
});

// ✅ 新方式（动态叶子索引）
const { activeBlankIndex } = useQuestionViewContext();
updateUserAnswer({
  content: newSelectedOptions,
  questionId: displayQuestion.questionId,
  blankIndex: activeBlankIndex, // 支持任意题型与母子题
});
```

### 导航 API 集成
ViewModel 可以访问导航状态：

```typescript
const {
  activeBlankIndex,      // 当前激活的题目索引
  currentActiveQuestion,  // 当前激活的题目信息
  leafPaths,             // 所有叶子路径数组
  goToNextLeaf           // 切换到下一个叶子（通常在UI层使用）
} = useQuestionViewContext();
```

### 兼容性保证
- **简单题**: `activeBlankIndex` 返回 0
- **母子题**: 返回对应的叶子扁平索引
- **现有代码**: 只需替换硬编码索引为 `activeBlankIndex`

### 迁移指南
1. 在 ViewModel 中导入 `useQuestionViewContext`
2. 获取 `activeBlankIndex` 和 `currentActiveQuestion`
3. 替换所有 `updateUserAnswer` 中的硬编码索引
4. 添加到依赖数组中（如果在 useCallback 中使用）

### 实际应用示例
```typescript
export function useChoiceQuestionViewModel() {
  const { questionStore } = useExerciseContext();
  const { updateUserAnswer } = questionStore;

  // 🆕 获取当前激活的题目索引
  const { activeBlankIndex, currentActiveQuestion } = useQuestionViewContext();

  const handleOptionClick = useCallback((optionKey: string) => {
    if (!currentActiveQuestion) return;

    // 🔧 更新Context中的答案状态
    updateUserAnswer({
      content: newSelectedOptions.join(","),
      questionId: currentActiveQuestion.questionId,
      // 🆕 使用当前激活的题目索引，支持母子题导航
      blankIndex: activeBlankIndex,
    });
  }, [activeBlankIndex, currentActiveQuestion, updateUserAnswer]);

  return { handleOptionClick };
}
```

ViewModel层作为新版答题系统的业务逻辑核心，通过 Context-First 零参数架构、Signal 响应式状态管理和策略模式验证，为整个系统提供了高性能、可维护、可测试的业务逻辑处理能力。新增的母子题导航系统进一步增强了系统的功能完整性和用户体验。