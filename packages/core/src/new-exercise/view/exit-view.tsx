import { StudyType } from "@repo/core/enums";
import { ExerciseConfirmDialog } from "../components/exercise-dialog";
import { useExerciseContext } from "../context";
import { useQuestionViewContext } from "../context/question-view-context";
import { useExitViewModel } from "../viewmodels/exit-viewmodel";

export function ExitView() {
  const {
    onBack,
    studyType,

    questionStore,
  } = useExerciseContext();

  const { isExiting, handleConfirmExit, handleCancelExit } = useExitViewModel();
  const { userAnswerDataMap, isShowExitConfirm } = questionStore;
  const { saveWhiteboardData, uploadAllWhiteboardData } =
    useQuestionViewContext();

  const beforeExit = async () => {
    if (isExiting.value) {
      return;
    }
    isExiting.value = true;
    await saveWhiteboardData();
    await uploadAllWhiteboardData();
    console.log("beforeExit", userAnswerDataMap.value);

    handleConfirmExit();
  };

  return (
    <ExerciseConfirmDialog
      studyType={studyType || StudyType.AI_COURSE}
      config={{
        isVisible: isShowExitConfirm.value,
        type: "exitConfirm",
        onLeftBtnClick: beforeExit,
        onRightBtnClick: handleCancelExit,
        confirmLoading: isExiting.value,
      }}
    />
  );
}
