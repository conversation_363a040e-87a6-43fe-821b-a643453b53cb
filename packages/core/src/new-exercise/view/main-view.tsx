"use client";

import { StudyType } from "@repo/core/enums";
import { useEffect, useMemo } from "react";
import { BackButton, ProgressBar, TimerDisplay } from "../components";
import { ExerciseContextProvider, useExerciseContext } from "../context";
import { StudyTypeThemeProvider } from "../context/theme-context";
import { useClient } from "../hooks/useClient";
import { useQuestionStore } from "../store";
import type { ExerciseInitParams } from "../types/exercise-config";
import { useExitViewModel } from "../viewmodels/exit-viewmodel";
import { useTimerVM } from "../viewmodels/timer-vm";
import FeedbackView from "./feedback-view";
import { ExerciseHeaderActionView } from "./header-action-view";
import QuestionViewContent from "./question-view";
import { TransitionView } from "./transition-view";

function ExerciseViewContent() {
  // 🎯 从Context获取题目相关状态
  const { questionStore, nextTransition, isPreview, studyType } =
    useExerciseContext();

  const { listenDeviceBackAction } = useClient();

  const {
    transitionState,
    progressBarState,
    rootQuestion,
    exerciseDisplayConfig,
    previewCurrentIndex,
    timerState,
    initialProgress,
  } = questionStore;

  const { handleExitRequest } = useExitViewModel();

  const { isTimerActive, handleTimeUpdate } = useTimerVM();

  const showProgressBar = useMemo(() => {
    if (exerciseDisplayConfig?.progressBar?.hidden) {
      return false;
    }
    if (studyType === StudyType.WRONG_QUESTION_BANK) {
      return false;
    }
    return true;
  }, [exerciseDisplayConfig?.progressBar?.hidden]);

  const showTimer = useMemo(() => {
    if (exerciseDisplayConfig?.timer?.hidden) {
      return false;
    }
    if (studyType === StudyType.WRONG_QUESTION_BANK) {
      return false;
    }
    return true;
  }, [exerciseDisplayConfig?.timer?.hidden]);

  // 监听设备返回按钮
  useEffect(() => {
    const unlisten = listenDeviceBackAction?.((result) => {
      if (result.code === 0 && result.data?.event === "backPressed") {
        // 打开弹窗
        handleExitRequest();
      }
    });

    return () => {
      unlisten?.();
    };
  }, [handleExitRequest, listenDeviceBackAction]);

  return (
    <>
      <div
        className="font-resource-han-rounded new-exercise-page relative flex h-screen flex-col gap-y-0"
        style={{ backgroundColor: "var(--study-background)" }}
      >
        <div className="pt-8.5 relative flex px-8 py-1">
          <div className="flex h-10 items-center">
            <div className="debug-back-wrapper relative">
              <BackButton onClick={handleExitRequest} />
            </div>
            {showTimer && rootQuestion.value && (
              <TimerDisplay
                isActive={isTimerActive}
                onTimeUpdate={handleTimeUpdate}
                rootQuestion={rootQuestion}
                timeState={timerState}
                className="ml-1 h-6 leading-6"
              />
            )}
          </div>
          {/* 进度条绝对居中定位 */}
          {showProgressBar && (
            <div className="absolute left-1/2 top-1/2 -translate-x-1/2 translate-y-1/2">
              <ProgressBar
                {...progressBarState.value}
                progress={progressBarState.value?.progress || initialProgress.value}
                displayMode={exerciseDisplayConfig?.progressBar?.showMode}
                totalCount={exerciseDisplayConfig?.progressBar?.totalCount}
                currentIndex={previewCurrentIndex.value}
                className="progress-bar-exercise"
              />
            </div>
          )}
          <div className="relative ml-auto flex justify-end gap-1">
            {/* 在已提交答案或首次提交错误时显示"猜你想问"入口 */}
            <ExerciseHeaderActionView />
            {/* 添加反馈弹窗组件 */}
            <FeedbackView />
          </div>
        </div>
        <div className="flex-1 overflow-auto">
          <QuestionViewContent />
        </div>

        {/* 🔥 简化：TransitionView作为平级组件，只负责基础反馈 */}
        <TransitionView
          currentTransition={transitionState.value.currentTransition}
          nextTransition={nextTransition}
        />
      </div>
    </>
  );
}

export function ExerciseView(props: Omit<ExerciseInitParams, "isEnglish">) {
  const {
    studySessionId,
    studyType,
    initialQuestionData,
    isPreview,
    previewConfig,
    customVariables,
  } = props;

  const questionStore = useQuestionStore({
    ...props,
    // 默认为 true，只有在课中练习时会出现 false
    isExerciseActive: props.isExerciseActive ?? true,
  });

  const { initialQuestionInfo, initPreviewQuestion } = questionStore;

  if (!isPreview && (!studyType || !studySessionId)) {
    // Sentry.captureException(
    //   // new Error("studyType and studySessionId are required"),
    //   {
    //     extra: {
    //       studyType,
    //       studySessionId,
    //       url: window.location.href,
    //     },
    //   }
    // );
    // toast.error("studyType and studySessionId are required");
    // throw new Error("studyType and studySessionId are required");
  }

  useEffect(() => {
    if (isPreview && studyType !== StudyType.WRONG_QUESTION_BANK) {
      if (!previewConfig?.questionList?.length) {
        throw new Error("预览模式下 questionList 不能为空");
      }
      // 从预览配置中获取初始索引
      initPreviewQuestion(
        previewConfig?.initialIndex ?? 0,
        previewConfig?.questionId
      );
      return;
    }

    if (studyType === StudyType.WRONG_QUESTION_BANK && initialQuestionData) {
      // 🎯 错题本模式（预览或练习）：统一使用 initialQuestionData
      initialQuestionInfo(initialQuestionData);
      return;
    }

    if (initialQuestionData && !isPreview) {
      // 🎯 非预览模式的其他学习类型：使用传入的题目数据
      initialQuestionInfo(initialQuestionData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <StudyTypeThemeProvider
      studyType={studyType!}
      customVariables={customVariables}
    >
      <ExerciseContextProvider questionStore={questionStore}>
        <ExerciseViewContent />
      </ExerciseContextProvider>
    </StudyTypeThemeProvider>
  );
}
