import { useComputed } from "@preact-signals/safe-react";
import { useQuestionViewContext } from "@repo/core/new-exercise/context/question-view-context";
import { getQuestionStemFillPointUuids } from "@repo/core/new-exercise/utils/questionBlank";
import { useCallback } from "react";
import { FillBlankContent, MultiStage, SingleStage } from "../../components";
import { useExerciseContext } from "../../context";
import { useQuestionNavVM } from "../../viewmodels/question-nav-vm"; // ✅ 使用 VM

const SimpleQuestionView = () => {
  const { questionStore } = useExerciseContext();
  const { activeBlankIndex, rootQuestion, isAnswerCompletedStatus } =
    questionStore;
  const { updateActiveBlankIndex } = useQuestionViewContext();
  const { navOptions } = useQuestionNavVM(); // ✅ 用 VM 的 navOptions

  const handleNavChange = useCallback(
    async (val: number) => {
      updateActiveBlankIndex(val);
    },
    [updateActiveBlankIndex]
  );

  const questionStem = rootQuestion.value?.questionContent.questionStem ?? "";
  const fillPointUuids = getQuestionStemFillPointUuids(questionStem);
  if (fillPointUuids.length === 0) return <></>;

  return (
    <SingleStage
      mode={isAnswerCompletedStatus.value ? "review" : "default"}
      options={navOptions.value} // ✅ 不直接读底层 treeOptions
      value={activeBlankIndex.value}
      onChange={handleNavChange}
    />
  );
};

const ComplexQuestionView = () => {
  const { questionStore } = useExerciseContext();
  const { updateActiveQuestionIndex, updateActiveBlankIndex } =
    useQuestionViewContext();
  const {
    activeQuestionIndex,
    activeBlankIndex,
    currentInputMode,
    currentQuestion,
    currentQuestionAnswers,
    isAnswerCompletedStatus,
    rootQuestion,
  } = questionStore;

  const { navOptions } = useQuestionNavVM(); // ✅ 用 VM 的 navOptions

  const questionStemSub = useComputed(() => {
    const [first = 0] = activeQuestionIndex.value;

    return (
      rootQuestion.value?.subQuestionList?.[first]?.questionContent
        ?.questionStem || ""
    );
  });

  const questionStemSSub = useComputed(() => {
    const [first = 0, second = 0] = activeQuestionIndex.value;

    return (
      rootQuestion.value?.subQuestionList?.[first]?.subQuestionList?.[second]
        ?.questionContent.questionStem || ""
    );
  });

  const handleNavChange = useCallback(
    async (val: number[]) => {
      updateActiveQuestionIndex(val);
    },
    [updateActiveQuestionIndex]
  );

  const renderFillBlankContent = useCallback(
    (qStem?: string) => {
      return (
        <FillBlankContent
          isCompleted={isAnswerCompletedStatus.value}
          answers={currentQuestionAnswers.value ?? []}
          inputMode={currentInputMode.value}
          questionStem={qStem ?? ""}
          type="child_parent"
          questionId={currentQuestion.value?.questionId ?? ""}
          activeBlankIndex={activeBlankIndex.value}
          onBlankClick={async (index) => updateActiveBlankIndex(index)}
        />
      );
    },
    [
      currentQuestionAnswers.value,
      currentInputMode.value,
      currentQuestion.value?.questionId,
      activeBlankIndex.value,
      updateActiveBlankIndex,
      isAnswerCompletedStatus,
    ]
  );

  return (
    <>
      <MultiStage
        mode={isAnswerCompletedStatus.value ? "review" : "default"}
        options={navOptions.value} // ✅ 不直接读底层 treeOptions
        value={activeQuestionIndex.value}
        onChange={handleNavChange}
        renderExtraContent={renderFillBlankContent(questionStemSub.value ?? "")}
      />
      {questionStemSSub.value &&
        renderFillBlankContent(questionStemSSub.value ?? "")}
    </>
  );
};

const QuestionNavTab = () => {
  const { questionStore } = useExerciseContext();
  const { isParentChildQuestion } = questionStore;

  // VM 已经接管“已作答/判分”与 updateTreeOptions 的副作用
  // 这里只负责决定渲染哪一个视图
  if (isParentChildQuestion.value) {
    return <ComplexQuestionView />;
  }
  return <SimpleQuestionView />;
};

export default QuestionNavTab;
