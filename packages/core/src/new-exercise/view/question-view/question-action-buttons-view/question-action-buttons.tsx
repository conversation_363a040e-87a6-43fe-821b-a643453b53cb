"use client";

import { useComputed, useSignalEffect } from "@preact-signals/safe-react";
import { StudyType } from "@repo/core/enums";
import { useQuestionViewContext } from "@repo/core/new-exercise/context/question-view-context";
import { useActionButtonsVM } from "@repo/core/new-exercise/viewmodels/action-button-vm";
import Button from "@repo/ui/components/press-button";
import { WrongQuestionBookButton } from "../../../components/wrong-book/wrong-question-book-button";
import { useExerciseContext } from "../../../context";
import { NavigationButton } from "./navigation-button";

// 按钮配置接口

export const QuestionActionButtons = () => {
  const { questionStore, studyType } = useExerciseContext();
  const { isShowNextBlankButton, isShowNextQuestionButton } =
    useQuestionViewContext();
  const { questionStatus, transitionState, rootQuestion } = questionStore;

  const {
    shouldShowGiveUpButton,
    canSubmit,
    handleContinueClick,
    getContinueButtonText,
    handleSubmit,
    handleGiveUpClick,
    handleUncertainClick,
    handleWrongQuestionBookAction,
    handleButtonClick,
    handleSubmitSelfEvaluation,
    showSubmitButton,
    isCurrentInWrongBook,
    currentErrorReasonTags,
    currentNotes,
    wrongBookVm,
    isSubmitting,
    canSubmitSelfEvaluation,
  } = useActionButtonsVM();

  // 是否显示自评按钮
  const isShowSelfEvaluationButton = useComputed(() => {
    return !isShowNextBlankButton.value && !isShowNextQuestionButton.value;
  });

  // TODO: 目前显示读取计算值，后续优化
  useSignalEffect(() => {
    console.log(`canSubmit`, {
      isSubmitting: isSubmitting.value,
      canSubmit: canSubmit.value,
      isShowSelfEvaluationButton: isShowSelfEvaluationButton.value,
    });
  });

  // 已提交或放弃状态：显示继续按钮和错题本按钮
  if (
    questionStatus.value === "submitted" ||
    questionStatus.value === "giving_up"
  ) {
    return (
      <div className="flex items-center gap-4">
        {/* 错题本按钮 - 只有提供了错题本操作回调函数时才显示 */}
        {
          <WrongQuestionBookButton
            questionId={rootQuestion.value?.questionId}
            isInWrongBook={isCurrentInWrongBook}
            studyType={studyType}
            onToggleWrongQuestionBook={async (
              _questionId,
              _isCurrentlyInBook,
              options
            ) => {
              // 🔑 使用 Hook 返回的函数，确保状态刷新
              await handleWrongQuestionBookAction(options);
            }}
            currentErrorReasonTags={currentErrorReasonTags}
            currentNotes={currentNotes}
            wrongBookVm={wrongBookVm}
            onButtonClick={handleButtonClick}
          />
        }

        <Button
          color="study-theme"
          studyType={studyType}
          onClick={handleContinueClick}
          disabled={transitionState.value.isPlayingTransitions}
        >
          {getContinueButtonText}
        </Button>
      </div>
    );
  }

  // 等待自评状态：只有自评题型才会进入此状态
  if (questionStatus.value === "evaluating") {
    // 注意：系统判题的题目不会进入 evaluating 状态
    // 如果进入了这个状态，说明一定是需要自评的题型
    return (
      <>
        <NavigationButton />
        {isShowSelfEvaluationButton.value && (
          <Button
            color="study-theme"
            studyType={studyType}
            onClick={handleSubmitSelfEvaluation}
            // 提交状态和未完成所有自评时，禁用按钮
            disabled={isSubmitting.value || !canSubmitSelfEvaluation.value}
            loading={isSubmitting.value}
          >
            提交自评
          </Button>
        )}
      </>
    );
  }

  // 答题状态：显示相关按钮
  return (
    <>
      {/* 🔧 二次作答状态：显示错题本按钮 */}
      {questionStatus.value === "second_answering" && (
        <WrongQuestionBookButton
          questionId={rootQuestion.value?.questionId}
          isInWrongBook={isCurrentInWrongBook}
          studyType={studyType}
          onToggleWrongQuestionBook={async (
            _questionId,
            _isCurrentlyInBook,
            options
          ) => {
            // 🔑 使用 Hook 返回的函数，确保状态刷新
            await handleWrongQuestionBookAction(options);
          }}
          currentErrorReasonTags={currentErrorReasonTags}
          currentNotes={currentNotes}
          wrongBookVm={wrongBookVm}
          onButtonClick={handleButtonClick}
        />
      )}
      {/* 不确定/放弃作答按钮 */}
      {shouldShowGiveUpButton ? (
        <Button
          loading={isSubmitting.value}
          color={
            studyType === StudyType.EXPAND_EXERCISE ? "green" : "light-blue"
          }
          secondary
          onClick={handleGiveUpClick}
        >
          放弃作答
        </Button>
      ) : (
        <Button color="white" onClick={handleUncertainClick}>
          不确定
        </Button>
      )}
      <NavigationButton />
      {/* 🆕 提交按钮 - 仅在最后一问时显示 */}
      {showSubmitButton.value && (
        <Button
          color="study-theme"
          onClick={handleSubmit}
          studyType={studyType}
          disabled={!canSubmit.value || isSubmitting.value}
          loading={isSubmitting.value}
        >
          提交
        </Button>
      )}
    </>
  );
};
