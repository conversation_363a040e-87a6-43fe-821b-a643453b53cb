import { useExerciseContext } from "@repo/core/new-exercise/context";
import { useQuestionViewContext } from "@repo/core/new-exercise/context/question-view-context";
import Button from "@repo/ui/components/press-button";

export const NavigationButton = ({
  color = "study-theme",
}: {
  color?: "white" | "study-theme";
}) => {
  const { studyType } = useExerciseContext();
  const {
    isShowNextBlankButton,
    isShowNextQuestionButton,
    goToNextBlank,
    goToNextQuestion,
  } = useQuestionViewContext();

  return (
    <>
      {/* 🆕 下一空按钮 - 仅在有多个空且非最后一空时显示 */}
      {isShowNextBlankButton.value && (
        <Button
          studyType={studyType}
          color={color}
          onClick={goToNextBlank}
          className="action-next-blank"
        >
          下一空
        </Button>
      )}

      {/* 🆕 下一问按钮 - 仅在有多个题目且非最后一问时显示 */}
      {isShowNextQuestionButton.value && (
        <Button
          studyType={studyType}
          color={color}
          onClick={goToNextQuestion}
          className="action-next-subQuestion"
        >
          下一问
        </Button>
      )}
    </>
  );
};
