import { useComputed } from "@preact-signals/safe-react";
import { useCallback } from "react";
import { InputModeSwitcher } from "../../components/answer-area/InputModeSwitcher";
import { useExerciseContext } from "../../context";
// import { useQuestionViewContext } from "../../context/question-view-context";
import IconInfo from "@repo/core/public/assets/stu-exercise/icons/info.svg";
import { MultipleAnswerPrompts } from "../../components/answer-area/multipleAnswerPrompts";
import { InputModeType, QuestionRenderType } from "../../enums";
import { shouldShowAnswerTabs } from "../../utils";
import { useFillBlankQuestionViewModel } from "../../viewmodels/fill-blank-question-vm";
import { ChoiceQuestionView } from "./choice-answer-area-view";
import { FillBlankAnswerAreaView } from "./fill-blank-answer-area-view";
import QuestionNavTab from "./question-nav-view";

const AnsweringArea = () => {
  const { questionStore, isPreview = false, isEnglish } = useExerciseContext();
  // const { handleModeChange } = useQuestionViewContext();
  const { handleModeChange } = useFillBlankQuestionViewModel();
  const { isAnswerCompletedStatus, currentInputMode } = questionStore;
  const {
    rootQuestion,
    currentQuestion,
    isSystemGradedQuestion,
    rootQuestionRenderType,
    isObjectiveQuestion,
    isSubjectiveQuestion,
    lastSubmitResult,
    questionRenderType,
    isParentChildQuestion,
  } = questionStore;
  // 只有填空和解答题才有模式切换，英语填空题没有

  const handleModeChangeFn = useCallback(
    async (val: InputModeType) => {
      if (isPreview) return;

      handleModeChange(val);
    },
    [isPreview, handleModeChange]
  );
  const showNavTab = useComputed(() => {
    if (!rootQuestion.value) return false;
    return shouldShowAnswerTabs(rootQuestion.value);
  });

  const isShowMultipleAnswerPrompts = useComputed(() => {
    const isParentChild =
      rootQuestionRenderType.value === QuestionRenderType.PARENT_CHILD;
    const isFinalAnswer = lastSubmitResult.value?.isFinalAnswer === false;

    return !isParentChild && isFinalAnswer && isObjectiveQuestion.value;
  });

  if (!currentQuestion.value) return null;
  return (
    <div className="mb-0">
      {isShowMultipleAnswerPrompts.value && <MultipleAnswerPrompts />}
      {/* 输入模式切换，只有是填空、问答、非系统判题、非母子题时显示在整道题的上方 */}
      {isSubjectiveQuestion.value &&
        !isParentChildQuestion.value &&
        !isAnswerCompletedStatus.value && (
          <InputModeSwitcher
            isEnglish={isEnglish.value}
            isQa={questionRenderType.value === QuestionRenderType.QA}
            inputMode={currentInputMode}
            onModeChange={handleModeChangeFn}
            className={"mb-3"}
          />
        )}

      {/* 是否显示题目导航组件 */}
      {showNavTab.value && <QuestionNavTab />}

      {/* 选择题、判断题渲染组件 */}
      {isObjectiveQuestion.value && <ChoiceQuestionView />}
      {isShowMultipleAnswerPrompts.value && (
        <div className="flex h-4 items-center gap-1 text-xs text-zinc-800/40">
          <IconInfo />
          第2次作答不计入最终成绩哦～
        </div>
      )}

      {/* 主观题 */}
      {isSubjectiveQuestion.value && (
        <>
          {/* 输入模式切换器，是母子题时显示在子题的上方 */}
          {!isAnswerCompletedStatus.value && isParentChildQuestion.value && (
            <InputModeSwitcher
              isEnglish={isEnglish.value}
              isQa={questionRenderType.value === QuestionRenderType.QA}
              inputMode={currentInputMode}
              onModeChange={handleModeChangeFn}
              type="parent_child"
            />
          )}

          {/* 主观题的作答区，包含输入、拍照、白板等 */}
          <FillBlankAnswerAreaView />
        </>
      )}
    </div>
  );
};

export default AnsweringArea;
