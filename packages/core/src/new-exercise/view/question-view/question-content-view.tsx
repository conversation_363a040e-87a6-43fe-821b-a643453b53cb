"use client";

import { useCallback, useMemo } from "react";
import { FillBlankContent, TagBar } from "../../components";
import { useExerciseContext } from "../../context/exercise-context";
import { useQuestionViewContext } from "../../context/question-view-context";
import { useWrongBookContext } from "../../context/wrong-book-context";
const QuestionContent = () => {
  // 从ExerciseContext获取错题本相关数据
  const { questionStore } = useExerciseContext();
  const { updateActiveBlankIndex, questionContentRef } =
    useQuestionViewContext();
  const {
    activeBlankIndex,
    currentInputMode,
    rootQuestion,
    currentQuestionAnswers,
    currentQuestion,
    exerciseParams,
    initialStudentAnswer,
    isAnswerCompletedStatus,
    isParentChildQuestion,
  } = questionStore;
  const questionStem = rootQuestion.value?.questionContent?.questionStem ?? "";
  const { studyType, isPreview } = exerciseParams;

  const handleBlankClick = useCallback(
    async (index: number) => {
      updateActiveBlankIndex(index);
    },
    [updateActiveBlankIndex]
  );

  // 🆕 获取错题本 ViewModel 来获取当前题目的错因标签
  const wrongBookVm = useWrongBookContext();
  const questionRecords = wrongBookVm?.questionRecords;

  // 🆕 获取当前题目的错因标签 - 使用 Signal 确保响应性
  const currentQuestionId = currentQuestion.value?.questionId;

  // 直接从 Signal 获取数据，确保响应性
  const records = questionRecords?.value;
  const currentErrorReasonTags = useMemo(() => {
    if (currentQuestionId == null || currentQuestionId.trim() === "") {
      return [];
    }

    // 🔑 检查本地状态中题目是否被移出错题本
    const record = records?.get(currentQuestionId);
    if (record?.isRemovedFromWrongBook === true) {
      // 如果题目已被移出错题本，不显示任何错因标签
      return [];
    }

    // 🔑 如果用户进行过错题本操作（有本地记录），优先使用用户的选择
    if (record != null) {
      // 用户选择了标签 → 显示用户选择的标签
      // 用户没选择标签（空数组）→ 不显示标签（即使 API 有默认标签）
      return record.errorReasonTags ?? [];
    }

    // 🔑 如果用户没有进行过错题本操作，使用 API 返回的默认标签
    const apiTags = currentQuestion.value?.wrongQuestionInfo?.userQuestionTags;
    if (apiTags != null && apiTags.length > 0) {
      return apiTags;
    }

    // 🔑 如果没有任何标签，返回空数组（不显示）
    return [];
  }, [
    records,
    currentQuestionId,
    currentQuestion.value?.wrongQuestionInfo?.userQuestionTags,
  ]);

  return (
    <>
      {/* 白色主背景 */}
      <div className="question-main-content relative z-10 h-full w-full rounded-t-xl border border-[#F3F2F2] bg-white px-6 pb-3">
        {/* 题目内容 */}
        <div
          className="mt-3 h-full max-h-full w-full overflow-y-auto pt-5"
          ref={questionContentRef}
        >
          <TagBar
            questionTypeName={rootQuestion.value?.questionTypeName}
            questionTags={rootQuestion.value?.questionTags || []}
            questionSourceInfo={rootQuestion.value?.questionTag}
            studyType={studyType}
            isPreview={isPreview}
            errorReasonTags={currentErrorReasonTags}
            answerTimes={
              (
                initialStudentAnswer.value as {
                  exerciseStats?: {
                    answerTimes?: number;
                    wrongTimes?: number;
                    inWrongNotebook?: boolean;
                  };
                }
              )?.exerciseStats?.answerTimes ??
              initialStudentAnswer.value?.answerTimes
            }
            wrongTimes={
              // 🔧 优先使用当前题目的 exerciseStats，然后使用兜底字段
              // 注意：学生答案的 exerciseStats 代表下一题的作答结果，不应用于当前题
              currentQuestion.value?.exerciseStats?.wrongTimes ??
              initialStudentAnswer.value?.wrongTimes
            }
          />
          <FillBlankContent
            isCompleted={isAnswerCompletedStatus.value}
            answers={currentQuestionAnswers.value ?? []}
            inputMode={currentInputMode.value}
            questionStem={questionStem}
            questionId={currentQuestion.value?.questionId ?? ""}
            type={isParentChildQuestion.value ? "child_parent" : ""}
            activeBlankIndex={activeBlankIndex.value}
            onBlankClick={handleBlankClick}
          />
        </div>
      </div>
    </>
  );
};

export default QuestionContent;
