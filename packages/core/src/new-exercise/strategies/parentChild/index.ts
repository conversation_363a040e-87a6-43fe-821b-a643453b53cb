import { CommonQuestion } from "@repo/core/types";
import { CONFIRM_DIALOG_CONFIGS } from "../../components/exercise-dialog";
import type { StudyQuestionInfo } from "../../types/question";
import { filterMap } from "../../utils/map-utils";
import { getQuestionRenderType } from "../../utils/question-is";
import {
  BaseQuestionStrategy,
  BeforeSubmitValidationResult,
  SubmitValidationContext,
} from "../base-strategy";
import { getStrategy, QuestionRenderType } from "../strategy-factory";

// ========== 母子题策略 ==========
/**
 * 母子题策略类
 * 处理母子题的答案验证、格式化等逻辑
 */

export class ParentChildStrategy extends BaseQuestionStrategy {
  readonly type = QuestionRenderType.PARENT_CHILD;
  readonly name = "母子题策略";
  readonly description = "处理母子题的答案验证、格式化等逻辑";

  // ========== 核心方法实现 ==========
  validateBeforeSubmit(
    question: CommonQuestion,
    _context?: SubmitValidationContext
  ): BeforeSubmitValidationResult {
    const { questionMetaMap, userAnswerData } = _context || {};

    const cleanedAllQuestionMetaMap = filterMap(
      questionMetaMap || new Map(),
      (meta) => !meta.questionRef.subQuestionList?.length
    );

    // 母子题需要根据子题类型进行校验
    if (!userAnswerData) {
      console.error("[ParentChildStrategy] 无法获取统一答案状态，阻止提交");
      return {
        canDirectSubmit: false,
        needsConfirmation: false,
      };
    }

    if (!question.subQuestionList || question.subQuestionList.length === 0) {
      console.warn("[ParentChildStrategy] 母子题没有子题列表");
      return {
        canDirectSubmit: true,
        needsConfirmation: false,
      };
    }

    // 真正的策略复用：为每个子题获取对应的策略并验证
    for (const [id, qMeta] of cleanedAllQuestionMetaMap || []) {
      const subQuestionType = getQuestionRenderType(qMeta.questionRef);
      const subStrategy = getStrategy(subQuestionType);

      if (!subStrategy) {
        console.warn(
          `[ParentChildStrategy] 无法获取子题策略: ${subQuestionType}`
        );
        continue;
      }

      // 调用子题策略的验证方法（类型转换：CommonQuestion -> StudyQuestionInfo）
      const subResult = subStrategy.validateBeforeSubmit(
        qMeta.questionRef,
        _context
      );

      // 如果任何一个子题需要确认，整个母题就需要确认
      if (subResult.needsConfirmation) {
        const isBlankPartMiss =
          subResult.confirmConfig?.title ==
          CONFIRM_DIALOG_CONFIGS.subjectivePartEmpty.title;

        if (isBlankPartMiss) {
          return subResult;
        }

        return {
          canDirectSubmit: false,
          needsConfirmation: true,
          confirmConfig: {
            ...(subResult.confirmConfig ||
              CONFIRM_DIALOG_CONFIGS.subjectiveEmpty),
            title: "还有题目未作答，确认提交吗？",
          },
        };
      }

      // 如果任何一个子题不能直接提交，整个母题就不能直接提交
      if (!subResult.canDirectSubmit) {
        return {
          canDirectSubmit: false,
          needsConfirmation: false,
        };
      }
    }

    // 所有子题都可以直接提交
    return {
      canDirectSubmit: true,
      needsConfirmation: false,
    };
  }

  preProcess(question: StudyQuestionInfo): StudyQuestionInfo {
    // 预处理：解析子题信息
    return {
      ...question,
      // 可以在这里添加子题解析逻辑
    };
  }

  postProcess(_question: StudyQuestionInfo, result: unknown): unknown {
    // 后处理：整合子题结果
    return result;
  }
}
