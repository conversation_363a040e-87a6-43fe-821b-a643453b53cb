import { SelfEvaluateType } from "../../enums";
import {
  ACTIVE_EVAL_STYLE,
  COLOR,
  COMPLETED_ACTIVE,
  COMPLETED_INACTIVE,
  NUMBER_INDICATOR_BASE,
  SVG_ICON_HTML,
} from "./enum";
/** 当前空的评测结果 */
const getEvalResult = (
  i: number,
  isCompleted: boolean,
  selfEvaluation: SelfEvaluateType[]
): SelfEvaluateType | null => {
  if (!isCompleted || !selfEvaluation) return null;
  return selfEvaluation[i] ?? null;
};

const resolveBlankClasses = ({
  evalResult,
  isActive,
  isCompleted,
  highlightWithLiteBg,
}: {
  evalResult: SelfEvaluateType | null;
  isActive: boolean;
  isCompleted: boolean;
  highlightWithLiteBg: boolean;
}) => {
  console.log(666666, evalResult, isActive, isCompleted);
  if (isCompleted && evalResult) {
    return isActive
      ? COMPLETED_ACTIVE[evalResult]
      : COMPLETED_INACTIVE[evalResult];
  }
  return {
    text: COLOR.baseText,
    border: isActive ? COLOR.activeBorder : COLOR.baseBorder,
    bg: isActive && highlightWithLiteBg ? COLOR.activeBgLite : "",
  };
};
/** 数字角标（圆点）类名，含已完成/未完成 & 激活/未激活逻辑 */
const resolveNumberIndicatorClasses = ({
  evalResult,
  isActive,
  isCompleted,
  hasValue: _hasValue, // 当前逻辑未使用，保留以保证签名不变
  colorClass, // 与空本体同色系
}: {
  evalResult: SelfEvaluateType | null;
  isActive: boolean;
  isCompleted: boolean;
  hasValue: boolean;
  colorClass: string;
}) => {
  // 激活态优先判断，缩短后续分支
  if (isActive) {
    // 已完成 + 有评测结果：按结果染色
    if (isCompleted && evalResult) {
      return `${NUMBER_INDICATOR_BASE} ${ACTIVE_EVAL_STYLE[evalResult]}`;
    }
    // 未完成激活：统一蓝底白字
    return `${NUMBER_INDICATOR_BASE} border-[#58C4FA] bg-[#58C4FA] text-white`;
  }

  // 非激活：沿用文字同色（其中可能包含 border 颜色，保持原逻辑）
  return `${NUMBER_INDICATOR_BASE} ${colorClass}`;
};

/** 表格检测 */
const isTableContent = (parts: (string | null)[]) =>
  parts.some(
    (part) =>
      typeof part === "string" &&
      (part.includes("<table") ||
        part.includes("<tr") ||
        part.includes("<td") ||
        part.includes("<th"))
  );

/** 表格段落 -> HTML，包含空白替换 */
const convertParagraphToHtml = ({
  parts,
  getAnswerValue,
  getEval,
  activeBlankIndex,
  isCompleted,
  hasSingleBlank,
  type,
}: {
  parts: (string | null)[];
  getAnswerValue: (i: number) => string;
  getEval: (i: number) => SelfEvaluateType;
  activeBlankIndex: number;
  isCompleted: boolean;
  hasSingleBlank: boolean;
  type?: string;
}) => {
  let idx = 0;

  const htmlString = parts
    .map((part) => {
      if (part !== null) return part;

      const i = idx++;
      const evalResult = getEval(i);
      const isActive = activeBlankIndex === i;
      const {
        text: colorClass,
        border: borderClass,
        bg: bgClass,
      } = resolveBlankClasses({
        evalResult,
        isActive,
        isCompleted,
        highlightWithLiteBg: false, // 表格场景无淡蓝背景
      });

      // 表格中答案展示与数字角标
      const answerValue = getAnswerValue(i);
      const hasValue = Boolean(answerValue);
      const isValueClass = hasValue ? "ml-3" : "";

      const numberIndicatorClass = resolveNumberIndicatorClasses({
        evalResult,
        isActive,
        isCompleted,
        hasValue,
        colorClass,
      });

      const iconHtml =
        isCompleted && evalResult ? SVG_ICON_HTML[evalResult] : "";

      return `
        <span
          class="fill_blank_input_area border-b-1 relative mx-1 inline-flex h-8 w-[10.625rem] cursor-pointer items-center justify-center align-top ${borderClass} ${colorClass} ${bgClass || ""}"
          data-blank-index="${i}"
          onclick="window.handleBlankClick && window.handleBlankClick(${i})"
        >
          ${
            !hasSingleBlank && type !== "child_parent"
              ? `<span class="${numberIndicatorClass} ${isValueClass}">${i + 1}</span>`
              : ""
          }
          ${
            hasValue
              ? `<span class="fill_blank_answer_text px-1 ${
                  hasSingleBlank ? "" : "ml-2"
                } flex-1 truncate ${colorClass}">${answerValue}</span>`
              : ""
          }
          ${iconHtml ? `<span class="flex mr-3 fill_blank_evaluation_icon items-center">${iconHtml}</span>` : ""}
        </span>
      `;
    })
    .join("");
  // 直接返回HTML字符串，数学公式将由FormatMath组件处理
  return htmlString;
};

export {
  convertParagraphToHtml,
  getEvalResult,
  isTableContent,
  resolveBlankClasses,
  resolveNumberIndicatorClasses,
};
