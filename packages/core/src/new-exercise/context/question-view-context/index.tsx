import {
  batch,
  Signal,
  useComputed,
  useSignal,
} from "@preact-signals/safe-react";
import { IOption } from "@repo/core/new-exercise/components/common/number-selector";
import { buildQuestionStructure } from "@repo/core/new-exercise/utils/question-structure";
import React, {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useRef,
} from "react";
import { useExerciseContext } from "..";
import { InputModeType } from "../../enums";
import { useWhiteboardAnswer } from "../../hooks";
import {
  filterMap,
  getLastKeyValueFromMap,
  getNextEntryFromMap,
} from "../../utils/map-utils";
import { updateTreeOptions, type IItem } from "../../utils/question-structure";

type UseWhiteboardAnswer = ReturnType<typeof useWhiteboardAnswer>;

export interface QuestionViewContextType {
  // 左侧题目内容区域
  questionContentRef: React.RefObject<HTMLDivElement | null>;
  questionContentScrollToTop: () => void;

  updateActiveBlankIndex: (point: number, skipSave?: boolean) => void;
  updateActiveQuestionIndex: (index: number[]) => void;

  treeOptions: Signal<IOption[]>;
  updateTreeOptions: (args: {
    targetId?: IOption["id"];
    targetIdx?: number;
    item: IItem;
  }) => void;

  questionPaths: Signal<number[][]>;
  goToNextQuestion: () => void;
  goToNextBlank: () => void;
  whiteboardRef: React.RefObject<{
    exportPaths: () => Promise<{ update: boolean; paths: string }>;
    exportImage: () => Promise<string | null>;
  } | null>;

  // 白板相关
  saveWhiteboardData: UseWhiteboardAnswer["saveWhiteboardData"];
  uploadAllWhiteboardData: UseWhiteboardAnswer["uploadAllWhiteboardData"];

  isLastQuestion: Signal<boolean>;
  isLastBlank: Signal<boolean>;
  isShowNextQuestionButton: Signal<boolean>;
  isShowNextBlankButton: Signal<boolean>;
}

const QuestionViewContext = createContext<QuestionViewContextType>(
  {} as QuestionViewContextType
);
const useQuestionViewContext = () => useContext(QuestionViewContext);

interface QuestionViewContextProviderProps {
  children: ReactNode;
}
const QuestionViewProvider: React.FC<QuestionViewContextProviderProps> = ({
  children,
}) => {
  const { questionStore, isEnglish } = useExerciseContext();
  const {
    isAnswerCompletedStatus,
    currentQuestion,
    activeBlankIndex, // 新增
    activeQuestionIndex, // 新增
    currentInputMode, // 新增
    rootQuestion,
    currentQuestionBlankCount,
    userAnswerDataMap,
    isParentChildQuestion,
    allQuestionMetaMap,
  } = questionStore;

  const questionContentRef = useRef<HTMLDivElement>(null);
  const treeOptions = useSignal<IOption[]>([]);
  const questionPaths = useSignal<number[][]>([]); // 🆕 题目路径

  // 题目内容区域滚动到顶部
  const questionContentScrollToTop = useCallback(() => {
    if (questionContentRef.current) {
      questionContentRef.current.scrollTop = 0;
    }
  }, [questionContentRef]);

  const { saveWhiteboardData, uploadAllWhiteboardData, whiteboardRef } =
    useWhiteboardAnswer();

  // 更新树形选项状态的方法
  const handleUpdateTreeOptions = useCallback(
    ({
      targetId,
      targetIdx,
      item,
    }: {
      targetId?: IOption["id"];
      targetIdx?: number;
      item: IItem;
    }) => {
      const updated = updateTreeOptions(
        targetId != null
          ? { options: treeOptions.value, targetId, item }
          : { options: treeOptions.value, targetIdx, item }
      );
      treeOptions.value = updated;
    },
    [treeOptions]
  );

  const beforeLeaveAnswerArea = useCallback(
    async (type?: InputModeType) => {
      await saveWhiteboardData(type);
      // 是否已作答
    },
    [saveWhiteboardData]
  );

  /*
   * 更新题目索引
   * @param index 题目索引
   * 说明：题目索引更新时，如果索引相同，则不更新，如果索引不同，则更新
   * 调用此方法自动设置到第一个空，不要单独设置空的 index
   */
  const updateActiveQuestionIndex = useCallback(
    async (index: number[]) => {
      // 对比数组是否相等
      if (index.toString() === activeQuestionIndex.value.toString()) return;
      await beforeLeaveAnswerArea().finally(() => {
        activeQuestionIndex.value = index;
        activeBlankIndex.value = 0;
      });
    },
    [beforeLeaveAnswerArea, activeQuestionIndex, activeBlankIndex]
  );

  const processParentQuestion = useCallback(() => {
    const question = rootQuestion.value;

    if (!question) {
      treeOptions.value = [];
      questionPaths.value = []; // 🆕 同时清空题目路径
      questionStore.allQuestionMetaMap.value = new Map(); // 🆕 清空元数据
      activeQuestionIndex.value = [];
      return;
    }

    // 🆕 使用纯函数一次性构建所有题目结构数据
    const {
      questionPaths: paths,
      allQuestionMetaMap,
      treeOptions: treeOptionsData,
      userAnswerDataMap: newUserAnswerDataMap,
    } = buildQuestionStructure(
      question,
      userAnswerDataMap.value,
      isEnglish.value
    );

    // 🆕 使用 batch 一次性更新所有 signals，避免多次渲染
    batch(() => {
      treeOptions.value = treeOptionsData;
      questionPaths.value = paths;
      questionStore.allQuestionMetaMap.value = allQuestionMetaMap;
      userAnswerDataMap.value = newUserAnswerDataMap;
      // 设置导航索引
      activeQuestionIndex.value = paths[0] || [];
    });
  }, [
    rootQuestion.value,
    userAnswerDataMap,
    questionPaths,
    questionStore.allQuestionMetaMap,
    activeQuestionIndex,
    isEnglish,
    treeOptions,
  ]);

  // 母子题的numberSelector的配置选项卡内容
  useEffect(() => {
    if (rootQuestion.value) {
      processParentQuestion();
    }
  }, [rootQuestion.value, processParentQuestion]);

  const updateActiveBlankIndex = useCallback(
    async (point: number, skipSave?: boolean) => {
      if (skipSave) {
        activeBlankIndex.value = point;
        return;
      }

      await beforeLeaveAnswerArea().finally(() => {
        activeBlankIndex.value = point;
      });
    },
    [beforeLeaveAnswerArea, activeBlankIndex]
  );

  /**
   * 判断是否为最后一问
   * @returns 是否为最后一问
   */
  const isLastQuestion = useComputed(() => {
    // const clearAllQuestionMetaMap = filterMap(
    //   allQuestionMetaMap.value,
    //   (meta) => !meta.questionRef.subQuestionList?.length
    // );

    const { lastKey } = getLastKeyValueFromMap(allQuestionMetaMap.value);

    return currentQuestion.value?.questionId === lastKey;
  });

  /**
   * 判断是否为最后一空
   * @returns 是否为最后一空
   */
  const isLastBlank = useComputed(
    () =>
      currentQuestionBlankCount.value == 1 ||
      activeBlankIndex.value === currentQuestionBlankCount.value - 1
  );

  /**
   * 判断是否显示下一问按钮
   * @returns 是否显示下一问按钮
   */
  const isShowNextQuestionButton = useComputed(() => {
    return (
      isLastBlank.value &&
      questionPaths.value.length > 1 &&
      !isLastQuestion.value
    );
  });

  /**
   * 判断是否显示下一空按钮
   * @returns 是否显示下一空按钮
   */
  const isShowNextBlankButton = useComputed(() => {
    // 空大于1，且不是最后一空，则显示下一空按钮（如果显示下一问则不显示下一空，下一空仅在非子母题的多空题展示）

    return (
      currentQuestionBlankCount.value > 1 &&
      !isLastBlank.value &&
      !isShowNextQuestionButton.value
    );
  });

  /**
   * 跳转到下一空
   */
  const goToNextBlank = (): void => {
    if (!isShowNextBlankButton.value) return;
    updateActiveBlankIndex(activeBlankIndex.value + 1);
  };

  /**
   * 跳转到下一问
   */
  const goToNextQuestion = (): void => {
    if (!isShowNextQuestionButton.value) return;
    const clearAllQuestionMetaMap = filterMap(
      allQuestionMetaMap.value,
      (meta) => !meta.questionRef.subQuestionList?.length
    );
    const { nextValue } = getNextEntryFromMap(
      clearAllQuestionMetaMap,
      currentQuestion.value?.questionId
    );
    updateActiveQuestionIndex(nextValue?.path || []);
  };

  const contextValue = {
    // 左侧题目内容区域
    questionContentRef,
    questionContentScrollToTop,
    // 白板相关
    whiteboardRef,
    beforeLeaveAnswerArea,
    saveWhiteboardData,
    uploadAllWhiteboardData,

    activeBlankIndex,
    updateActiveBlankIndex,
    activeQuestionIndex,
    updateActiveQuestionIndex,
    treeOptions: treeOptions,
    updateTreeOptions: handleUpdateTreeOptions,
    isAnswerCompletedStatus,

    // 🆕 极简导航API
    questionPaths,
    currentQuestionBlankCount,
    goToNextQuestion,
    goToNextBlank,
    currentQuestion,
    isLastQuestion,
    isLastBlank,
    isShowNextQuestionButton,
    isShowNextBlankButton,
  };

  return (
    <QuestionViewContext value={contextValue}>{children}</QuestionViewContext>
  );
};

export { QuestionViewProvider, useQuestionViewContext };
