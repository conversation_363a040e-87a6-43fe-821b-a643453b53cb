import { useSignal } from "@preact-signals/safe-react";
import * as Sentry from "@sentry/nextjs";
import { useCallback, useRef } from "react";
import { v4 as uuid } from "uuid";
import { WhiteboardRef } from "../components/answer-area/fill-blank/Whiteboard";
import { useExerciseContext } from "../context/exercise-context";
import { InputModeType } from "../enums";
import { WhiteBoardData } from "../types";
import { retry, useImageUpload } from "./useImageUpload";

async function base64ToFile(dataUrl: string, fileName = "preview.webp") {
  const res = await fetch(dataUrl);
  const blob = await res.blob();
  return new File([blob], fileName, { type: blob.type });
}

/**
 * 白板答案管理 Hook - 重构版
 *
 * 功能：
 * 1. 直接操作 userAnswerDataMap，统一数据源
 * 2. 白板缓存数据存储在 Answer.whiteBoardData 中
 * 3. 上传成功后清除缓存，保留 content（图片URL）
 * 4. 消除数据重复和key拼接问题
 */
export function useWhiteboardAnswer() {
  const whiteboardRef = useRef<WhiteboardRef>(null);

  const { questionStore } = useExerciseContext();
  const {
    currentQuestion,
    userAnswerDataMap,
    updateUserAnswer,
    currentInputMode,
    activeBlankIndex,
  } = questionStore;

  // 使用现有的图片上传 hook
  const { clearUploadData, handleImageUpload } = useImageUpload({
    signatureUrl: "/api/v1/common/oss/token",
    isCheckPicture: false,
  });

  const uploadingMap = useSignal<Record<string, boolean>>({});

  const transAndUploadWhiteboardImage = useCallback(
    async (data: {
      questionId: string;
      blankIndex: number;
      type: InputModeType;
      whiteBoardData: WhiteBoardData;
    }) => {
      uploadingMap.value[`${data.questionId}_${data.blankIndex}`] = true;
      const fileName = `whiteboard_preview_${uuid()}`;
      const imageFile = await base64ToFile(
        data.whiteBoardData?.imageData || "",
        `${fileName}.webp`
      );
      Sentry.addBreadcrumb({
        message: "转换白板数据并上传白板图片",
        data: {
          imageFile: imageFile,
          blankAnswerData: data,
        },
      });

      if (!imageFile) return;

      const doUpload = async () => {
        // 使用现有的图片上传方法
        const url = await handleImageUpload({
          id: fileName,
          file: imageFile,
          preview: "",
          status: "pending",
        });

        const currentInputMode =
          userAnswerDataMap.value.get(data.questionId)?.[data.blankIndex]
            ?.type || InputModeType.WHITEBOARD;

        if (url) {
          // 上传成功后清理数据
          uploadingMap.value[`${data.questionId}_${data.blankIndex}`] = false;
          clearUploadData();

          return {
            url,
            questionId: data.questionId,
            blankIndex: data.blankIndex,
          };
        }
      };

      // 构造“带重试”的任务，并统一错误收敛，不抛出未处理拒绝
      const task = doUpload().catch((_err) =>
        retry(
          () => {
            Sentry.addBreadcrumb({
              message: "上传白板图片失败重试",
              data: { blankAnswerData: data },
            });
            return doUpload();
          },
          3,
          50
        ).catch((error) => {
          const msg = "白板图片上传失败";
          Sentry.captureException(error, {
            level: "fatal",
            extra: { message: msg, blankAnswerData: data },
          });
          return null; // 收敛失败为 null，避免未处理拒绝
        })
      );

      return await task; // 非 sync 返回真实 Promise，给调用方等待
    },
    [clearUploadData, handleImageUpload, uploadingMap, userAnswerDataMap]
  );

  // 提交前确保所有白板数据已上传 - 优化版：批量收集+并发上传
  const uploadAllWhiteboardData = useCallback(async (): Promise<void> => {
    // 一次性收集所有需要上传的白板数据
    const pendingUploads: Array<{
      questionId: string;
      index: number;
      whiteBoardData: WhiteBoardData;
    }> = [];
    Sentry.addBreadcrumb({
      message: "检查所有白板数据是否需要上传",
      data: {
        pendingUploads,
      },
    });

    // 快速遍历收集
    for (const [questionId, blankDatas] of userAnswerDataMap.value) {
      blankDatas.forEach((answer, index) => {
        // 上传中的不再重复处理
        if (uploadingMap.value[`${questionId}_${index}`]) return;
        if (
          answer.type === InputModeType.WHITEBOARD &&
          answer.whiteBoardData &&
          !answer.whiteBoardData.imageUrl
        ) {
          pendingUploads.push({
            questionId,
            index,
            whiteBoardData: answer.whiteBoardData,
          });
        }
      });
    }

    // 如果没有待上传项，直接返回
    if (pendingUploads.length === 0) return;

    // 批量并发上传（限制并发数避免服务器压力）
    const CONCURRENT_LIMIT = 3;
    const uploadPromises: Promise<unknown>[] = [];

    for (let i = 0; i < pendingUploads.length; i += CONCURRENT_LIMIT) {
      const batch = pendingUploads.slice(i, i + CONCURRENT_LIMIT);
      const batchPromises = batch.map((upload) => {
        const task = transAndUploadWhiteboardImage({
          questionId: upload.questionId,
          blankIndex: upload.index,
          type: InputModeType.WHITEBOARD,
          whiteBoardData: upload.whiteBoardData,
        });
        task.then((res) => {
          if (res) {
            updateUserAnswer({
              questionId: upload.questionId,
              blankIndex: upload.index,
              type: InputModeType.WHITEBOARD,
              whiteBoardData: {
                update: false,
                paths: upload.whiteBoardData?.paths || "[]",
                imageData: upload.whiteBoardData?.imageData || "",
                imageUrl: res.url,
              },
            });
          }
        });

        return task;
      });
      uploadPromises.push(...batchPromises);
    }

    // 等待所有上传完成（或失败）
    await Promise.allSettled(uploadPromises);
  }, [
    userAnswerDataMap,
    transAndUploadWhiteboardImage,
    updateUserAnswer,
    uploadingMap,
  ]);

  // 保存白板数据（离开空时调用）
  const saveWhiteboardData = useCallback(
    async (type?: InputModeType) => {
      if (!whiteboardRef.current?.exportImage) return;
      const paths = await whiteboardRef.current?.exportPaths();

      if (!paths?.update) {
        return;
      }
      const isEmpty = paths?.paths === "[]";

      const image = isEmpty ? "" : await whiteboardRef.current?.exportImage();

      const answerData = {
        questionId: currentQuestion.value?.questionId || "",
        blankIndex: activeBlankIndex.value,
        type: type || InputModeType.WHITEBOARD,
        whiteBoardData: {
          update: true,
          paths: paths?.paths,
          imageData: image || "",
          imageUrl: "",
        },
      };

      Sentry.addBreadcrumb({
        message: "保存白板数据",
        data: {
          answerData,
        },
      });

      updateUserAnswer(answerData);

      // 静默调用
      setTimeout(() => {
        uploadAllWhiteboardData();
      });
    },
    [
      currentQuestion.value,
      activeBlankIndex,
      whiteboardRef,
      updateUserAnswer,
      uploadAllWhiteboardData,
    ]
  );

  return {
    whiteboardRef,
    // 业务逻辑
    saveWhiteboardData,
    uploadAllWhiteboardData,
  };
}
