import { useComputed } from "@preact-signals/safe-react";
import { useExerciseContext } from "@repo/core/new-exercise/context";
import { SelfEvaluateType } from "@repo/core/new-exercise/enums";
import { useCallback } from "react";
import { getAnswerContentWithInputMode } from "../utils/question-answer";

export interface SelfEvaluationState {
  autoEvaluate: boolean;
}

/**
 * 自评 Hook
 *
 * 职责：
 * - 管理自评状态
 * - 处理自评逻辑
 * - 提供自评相关方法
 * - 处理自评数据的初始化和更新
 */

export function useSelfEvaluation() {
  const { questionStore, isPreview, studyType } = useExerciseContext();
  const {
    updateSelfEvaluation,
    currentQuestionAnswers,
    userAnswerDataMap,
    currentQuestion,
    isSystemGradedQuestion,
    activeBlankIndex,
    questionStatus,
    navigationNextEvaluatingQuestionBlank,
    isAiCourseResume,
  } = questionStore;

  const autoEvaluate = isSystemGradedQuestion.value as boolean;

  // 🆕 更新单个自评项的函数
  const updateSingleSelfEvaluation = useCallback(
    (index: number, evalValue: SelfEvaluateType | null) => {
      const currentQuestionId = currentQuestion.value?.questionId;
      if (!currentQuestionId || currentQuestionId.trim() === "") return;

      if (evalValue !== null) {
        updateSelfEvaluation({
          questionId: currentQuestionId,
          blankIndex: index,
          selfEvaluation: evalValue,
        });
      }
    },
    [currentQuestion.value?.questionId, updateSelfEvaluation]
  );

  // 🆕 批量更新自评状态的函数
  const setSelfEvaluation = useCallback(
    (evaluation: SelfEvaluateType[] | null[]) => {
      evaluation.forEach((evalValue, index) => {
        updateSingleSelfEvaluation(index, evalValue);
      });
    },
    [updateSingleSelfEvaluation]
  );

  // 从 userAnswerData 中获取当前题目的自评数据
  const selfEvaluation = useComputed(() => {
    const currentQuestionId = currentQuestion.value?.questionId;

    const questionAnswers =
      userAnswerDataMap.value?.get(currentQuestionId!) || [];

    const res = questionAnswers.map((answer) => {
      if (
        questionStatus.value === "evaluating" &&
        isAiCourseResume.value &&
        !answer.selfEvaluation &&
        !answer.content
      ) {
        return SelfEvaluateType.wrong;
      }
      return answer.selfEvaluation as SelfEvaluateType;
    });

    return res;
  });

  // 自评处理函数
  const handleSelfEvaluate = useCallback(
    (type: SelfEvaluateType) => {
      try {
        const questionId = currentQuestion.value?.questionId;
        if (!questionId || questionId.trim() === "") {
          console.error("题目ID为空，无法更新自评状态");
          return;
        }

        updateSelfEvaluation({
          questionId,
          blankIndex: activeBlankIndex.value,
          selfEvaluation: type,
        });
        // navigationNextEvaluatingQuestionBlank();
      } catch (error) {
        console.error("自评状态更新失败:", error);
      }
    },
    [currentQuestion.value, updateSelfEvaluation, activeBlankIndex.value]
  );

  // 按钮是否被锁定
  const isButtonLocked = useComputed(() => {
    const currentAnswer =
      currentQuestionAnswers.value?.[activeBlankIndex.value];

    // 答题中使用的默认逻辑
    let validAnswerContent = currentAnswer
      ? getAnswerContentWithInputMode(currentAnswer)
      : "";

    // 课中恢复练习可能缺少完整的作答数据，判断 content 即可
    if (questionStatus.value === "evaluating") {
      validAnswerContent = currentAnswer?.content || "";
    }

    if (isPreview) {
      return true;
    }

    return (
      !validAnswerContent ||
      questionStatus.value === "submitted" ||
      autoEvaluate
    );
  });

  return {
    selfEvaluation,
    setSelfEvaluation,
    handleSelfEvaluate,
    isButtonLocked,
  };
}
