export function getLastKeyValueFromMap<T>(map: Map<string, T>): {
  lastKey: string;
  lastValue: T;
} {
  if (!(map instanceof Map) || map.size === 0)
    return { lastKey: "", lastValue: {} as T };

  // 取最后一个 entry
  const [lastKey, lastValue] = [...map.entries()].at(-1)!;
  return { lastKey, lastValue };
}

export const getNextEntryFromMap = <K, V>(
  map: Map<K, V>,
  key: K
): { nextKey: K; nextValue: V | undefined } => {
  let found = false;
  for (const [nextKey, nextValue] of map) {
    if (found) {
      return { nextKey, nextValue }; // 返回找到的下一个
    }
    if (nextKey === key) {
      found = true;
    }
  }
  return {
    nextKey: key,
    nextValue: map.get(key) ?? undefined,
  };
};

export const filterMap = <K, V>(
  map: Map<K, V>,
  predicate: (value: V) => boolean
) => {
  return new Map(
    Array.from(map.entries()).filter(([_, value]) => predicate(value))
  );
};
