# New-Exercise Store

基于 **Preact Signals** 的新版答题状态管理层，用于高性能管理答题过程中涉及的所有核心状态（题目、答案、计时、进度、动画等），并支持复杂的业务逻辑和多题型交互。

---

## 📂 文件结构

```
store/
├── question-store.tsx   # 核心 Hook 实现
├── index.ts             # 导出入口
└── README.md            # 说明文档
```

---

## 🎯 设计目标
1. **细粒度响应式**：使用 Signals 避免全局 Context 引起的无关组件重渲染  
2. **渐进式优化**：不破坏原有架构，可按需引入优化  
3. **统一状态管理**：集中维护答题过程的全部状态  
4. **类型安全**：全量 TypeScript 类型定义  
5. **性能可控**：使用 `peek()`、批量更新等手段降低渲染压力  

---

## 🏗 核心状态分类

| 分类 | 变量 | 描述 |
|------|------|------|
| 题目信息 | `rootQuestion` / `currentQuestion` | 根题目与当前激活题目 |
| 状态 | `questionStatus` | 当前答题状态（answering / evaluating / submitted 等） |
| 答案数据 | `userAnswerDataMap` | 按题目 ID 存储的用户答案 |
| 答案类型 | `userAnswerTypeMap` | 按题目 ID 存储的答案类型 |
| 白板数据 | `userAnswerWhiteBoardMap` | 按题目 ID + 空索引存储的白板数据 |
| 计时 | `timerState` | 当前题目的计时和总时长 |
| 进度 | `progressBarState` | 进度条显示状态 |
| 动画 | `transitionState` | 转场动画控制 |
| 结果 | `lastSubmitResult` | 上一次提交的结果数据 |

---

## 🔧 核心方法

| 方法 | 功能 |
|------|------|
| `updateUserAnswer(state)` | 更新指定题目的答案 |
| `clearUserAnswer()` | 清空当前题目的用户答案 |
| `updateSelfEvaluation(state)` | 更新自评结果 |
| `updateQuestionStatus(state)` | 修改答题状态 |
| `resetQuestionStateOnNext()` | 重置状态，进入下一题前调用（包含白板数据清空） |
| `initialQuestionInfo(data)` | 初始化题目信息和初始答案 |
| `initPreviewQuestion(question)` | 预览模式下初始化题目 |
| `handleNextQuestion()` | 跳转到下一题并重置状态 |
| `updateProgressBarState(state)` | 更新进度条状态 |
| `resetCurrentQuestionTime()` | 重置当前题目计时 |

---

## 📈 常用计算属性
- **题型判断**  
  `isChoiceQuestion`, `isMultipleChoiceQuestion`, `isObjectiveQuestion`, `isSubjectiveQuestion`, `isSelfEvaluationQuestion`, `isSystemGradedQuestion`
- **答案状态**  
  `isAnswerComplete` — 当前题目答案是否完整  
  `canSubmitSelfEvaluation` — 是否完成全部自评  
- **埋点参数**  
  `getBaseTrackParams()` — 提供当前题目的埋点基础参数（使用 `peek()` 避免无效订阅）  

---

## 🎨 白板状态管理

### 数据结构
```typescript
// 白板数据存储在 userAnswerWhiteBoardMap 中
// Key: `${questionId}_${blankIndex}`
// Value: WhiteBoardData
interface WhiteBoardData {
  questionId: string;
  whiteBoardData: Record<string, any>; // 白板 JSON 数据
  index: number;                       // 空的索引
  content: string;                     // 上传后的图片 URL
}
```

### 管理策略
1. **增量保存**：离开空时检查数据变化，变化则异步上传
2. **状态同步**：上传成功后自动同步到 `userAnswerDataMap`
3. **容错处理**：上传失败不阻塞用户操作，提交时重试
4. **自动清理**：切换题目时自动清空白板数据

### 集成方式
白板状态管理通过 `useWhiteboardAnswer` Hook 封装，不直接操作 Store：

```typescript
// 推荐使用方式
const { saveWhiteboardData, uploadAllWhiteboardData } = useWhiteboardAnswer();

// 不推荐直接操作 Store
// questionStore.userAnswerWhiteBoardMap.value.set(key, data); // ❌
```

---

## 🚀 使用示例
```tsx
import { useQuestionStore } from '@repo/core/new-exercise/store';

export const QuestionView = ({ params }) => {
  const store = useQuestionStore(params);

  const handleAnswer = () => {
    store.updateUserAnswer({
      questionId: store.currentQuestion.value!.questionId,
      blankIndex: 0,
      content: "我的答案"
    });
  };

  return (
    <div>
      <h3>{store.currentQuestion.value?.title}</h3>
      <button onClick={handleAnswer}>答题</button>
      <button disabled={!store.isAnswerComplete.value}>提交</button>
    </div>
  );
};
````

---

## ⚡ 性能优化建议

1. **避免无关渲染**：计算属性和组件绑定时，只订阅必要的信号
2. **减少对象创建**：更新状态时保留引用，尽量使用不可变更新模式
3. **批量更新**：在多状态更新时使用 `batch()` 包裹
4. **只读访问**：使用 `peek()` 获取值而不建立订阅关系
5. **即时清理**：题目切换时重置不必要的状态，防止内存泄漏

---

## 🛠 维护规范

* **最小化状态**：仅存储不可从其他状态推导出的数据
* **类型优先**：所有状态和方法均需定义 TypeScript 类型
* **副作用隔离**：业务副作用不要直接写入 store 中
* **可调试**：保留关键状态更新的日志（可在生产环境移除）

---

```

我保留了**架构背景 + 状态分类 + 方法列表 + 计算属性 + 示例 + 优化建议**，这样可读性和可维护性都比原版高，但不会太冗长。  

如果你愿意，我可以帮你把这个版本直接替换到 `README.md` 并自动生成 **方法表格** 和 **状态表格**，让它和 `question-store.tsx` 里的实现完全同步。  
这样文档就永远不会过时。  
你要我这么做吗？
```
